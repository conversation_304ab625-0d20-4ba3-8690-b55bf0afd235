const User = require('../models/User');
const Employee = require('../models/Employee');
const jwtUtils = require('../utils/jwt');
const database = require('../config/database');
const { validationResult } = require('express-validator');

class AuthController {
  // User registration
  static async signup(req, res) {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { username, email, password, role = 'employee' } = req.body;

      // Check if user already exists
      const existingUser = await User.findByUsername(username);
      if (existingUser) {
        return res.status(409).json({
          success: false,
          message: 'Username already exists'
        });
      }

      const existingEmail = await User.findByEmail(email);
      if (existingEmail) {
        return res.status(409).json({
          success: false,
          message: 'Email already exists'
        });
      }

      // Validate user data
      const validationErrors = User.validate({ username, email, password, role });
      if (validationErrors.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: validationErrors
        });
      }

      // Create user
      const user = await User.create({ username, email, password, role });

      // Generate tokens
      const tokens = jwtUtils.generateTokenPair(user);

      // Store session
      await AuthController.storeSession(user.id, tokens.accessToken, tokens.refreshToken, req);

      // Log audit
      await database.run(`
        INSERT INTO audit_logs (user_id, action, new_values, ip_address, user_agent)
        VALUES (?, ?, ?, ?, ?)
      `, [
        user.id,
        'USER_SIGNUP',
        JSON.stringify({ username, email, role }),
        req.ip,
        req.get('User-Agent')
      ]);

      res.status(201).json({
        success: true,
        message: 'User created successfully',
        data: {
          user: user.toJSON(),
          tokens
        }
      });

    } catch (error) {
      console.error('Signup error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // User login
  static async login(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { username, password } = req.body;

      // Find user
      const user = await User.findByUsername(username);
      if (!user) {
        return res.status(401).json({
          success: false,
          message: 'Invalid credentials'
        });
      }

      // Check if user is active
      if (!user.is_active) {
        return res.status(401).json({
          success: false,
          message: 'Account is deactivated'
        });
      }

      // Verify password
      const isValidPassword = await user.verifyPassword(password);
      if (!isValidPassword) {
        return res.status(401).json({
          success: false,
          message: 'Invalid credentials'
        });
      }

      // Update last login
      await user.updateLastLogin();

      // Generate tokens
      const tokens = jwtUtils.generateTokenPair(user);

      // Store session
      await AuthController.storeSession(user.id, tokens.accessToken, tokens.refreshToken, req);

      // Get user with employee details
      const userWithEmployee = await user.getWithEmployee();

      // Log audit
      await database.run(`
        INSERT INTO audit_logs (user_id, action, ip_address, user_agent)
        VALUES (?, ?, ?, ?)
      `, [
        user.id,
        'USER_LOGIN',
        req.ip,
        req.get('User-Agent')
      ]);

      res.json({
        success: true,
        message: 'Login successful',
        data: {
          user: userWithEmployee,
          tokens
        }
      });

    } catch (error) {
      console.error('Login error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // User logout
  static async logout(req, res) {
    try {
      const authHeader = req.headers.authorization;
      if (authHeader) {
        const token = jwtUtils.extractTokenFromHeader(authHeader);
        const tokenHash = jwtUtils.hashToken(token);

        // Remove session
        await database.run('DELETE FROM sessions WHERE token_hash = ?', [tokenHash]);

        // Log audit
        await database.run(`
          INSERT INTO audit_logs (user_id, action, ip_address, user_agent)
          VALUES (?, ?, ?, ?)
        `, [
          req.user?.id || null,
          'USER_LOGOUT',
          req.ip,
          req.get('User-Agent')
        ]);
      }

      res.json({
        success: true,
        message: 'Logout successful'
      });

    } catch (error) {
      console.error('Logout error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Refresh token
  static async refresh(req, res) {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        return res.status(400).json({
          success: false,
          message: 'Refresh token required'
        });
      }

      // Verify refresh token
      const decoded = jwtUtils.verifyRefreshToken(refreshToken);

      // Check if refresh token exists in database
      const refreshTokenHash = jwtUtils.hashToken(refreshToken);
      const session = await database.get(
        'SELECT * FROM sessions WHERE refresh_token_hash = ? AND expires_at > CURRENT_TIMESTAMP',
        [refreshTokenHash]
      );

      if (!session) {
        return res.status(401).json({
          success: false,
          message: 'Invalid or expired refresh token'
        });
      }

      // Get user
      const user = await User.findById(decoded.id);
      if (!user || !user.is_active) {
        return res.status(401).json({
          success: false,
          message: 'User not found or inactive'
        });
      }

      // Generate new tokens
      const tokens = jwtUtils.generateTokenPair(user);

      // Update session with new tokens
      const newTokenHash = jwtUtils.hashToken(tokens.accessToken);
      const newRefreshTokenHash = jwtUtils.hashToken(tokens.refreshToken);
      
      await database.run(`
        UPDATE sessions 
        SET token_hash = ?, refresh_token_hash = ?, last_used = CURRENT_TIMESTAMP
        WHERE id = ?
      `, [newTokenHash, newRefreshTokenHash, session.id]);

      res.json({
        success: true,
        message: 'Token refreshed successfully',
        data: { tokens }
      });

    } catch (error) {
      console.error('Refresh token error:', error);
      res.status(401).json({
        success: false,
        message: 'Invalid refresh token',
        error: error.message
      });
    }
  }

  // Get user profile
  static async getProfile(req, res) {
    try {
      const userWithEmployee = await req.user.getWithEmployee();

      res.json({
        success: true,
        data: { user: userWithEmployee }
      });

    } catch (error) {
      console.error('Get profile error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Update profile
  static async updateProfile(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { email } = req.body;
      const allowedUpdates = { email };

      // Remove undefined values
      Object.keys(allowedUpdates).forEach(key => 
        allowedUpdates[key] === undefined && delete allowedUpdates[key]
      );

      if (Object.keys(allowedUpdates).length === 0) {
        return res.status(400).json({
          success: false,
          message: 'No valid fields to update'
        });
      }

      // Update user
      await req.user.update(allowedUpdates);

      // Get updated user with employee details
      const updatedUser = await req.user.getWithEmployee();

      // Log audit
      await database.run(`
        INSERT INTO audit_logs (user_id, action, new_values, ip_address, user_agent)
        VALUES (?, ?, ?, ?, ?)
      `, [
        req.user.id,
        'PROFILE_UPDATE',
        JSON.stringify(allowedUpdates),
        req.ip,
        req.get('User-Agent')
      ]);

      res.json({
        success: true,
        message: 'Profile updated successfully',
        data: { user: updatedUser }
      });

    } catch (error) {
      console.error('Update profile error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Change password
  static async changePassword(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { currentPassword, newPassword } = req.body;

      // Verify current password
      const isValidPassword = await req.user.verifyPassword(currentPassword);
      if (!isValidPassword) {
        return res.status(400).json({
          success: false,
          message: 'Current password is incorrect'
        });
      }

      // Update password
      await req.user.updatePassword(newPassword);

      // Invalidate all sessions except current
      const currentTokenHash = jwtUtils.hashToken(
        jwtUtils.extractTokenFromHeader(req.headers.authorization)
      );
      
      await database.run(
        'DELETE FROM sessions WHERE user_id = ? AND token_hash != ?',
        [req.user.id, currentTokenHash]
      );

      // Log audit
      await database.run(`
        INSERT INTO audit_logs (user_id, action, ip_address, user_agent)
        VALUES (?, ?, ?, ?)
      `, [
        req.user.id,
        'PASSWORD_CHANGE',
        req.ip,
        req.get('User-Agent')
      ]);

      res.json({
        success: true,
        message: 'Password changed successfully'
      });

    } catch (error) {
      console.error('Change password error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Store session in database
  static async storeSession(userId, accessToken, refreshToken, req) {
    const tokenHash = jwtUtils.hashToken(accessToken);
    const refreshTokenHash = jwtUtils.hashToken(refreshToken);
    const expiresAt = jwtUtils.getTokenExpiration(accessToken);

    await database.run(`
      INSERT INTO sessions (user_id, token_hash, refresh_token_hash, expires_at, ip_address, user_agent)
      VALUES (?, ?, ?, ?, ?, ?)
    `, [
      userId,
      tokenHash,
      refreshTokenHash,
      expiresAt.toISOString(),
      req.ip,
      req.get('User-Agent')
    ]);
  }

  // Get user sessions
  static async getSessions(req, res) {
    try {
      const sessions = await database.all(`
        SELECT id, ip_address, user_agent, created_at, last_used, expires_at
        FROM sessions 
        WHERE user_id = ? AND expires_at > CURRENT_TIMESTAMP
        ORDER BY last_used DESC
      `, [req.user.id]);

      res.json({
        success: true,
        data: { sessions }
      });

    } catch (error) {
      console.error('Get sessions error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Revoke session
  static async revokeSession(req, res) {
    try {
      const { sessionId } = req.params;

      await database.run(
        'DELETE FROM sessions WHERE id = ? AND user_id = ?',
        [sessionId, req.user.id]
      );

      res.json({
        success: true,
        message: 'Session revoked successfully'
      });

    } catch (error) {
      console.error('Revoke session error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }
}

module.exports = AuthController;
