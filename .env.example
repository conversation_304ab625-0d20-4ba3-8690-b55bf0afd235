# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DB_TYPE=sqlite
DB_PATH=./database/hrm_system.db

# For Production PostgreSQL (uncomment and configure)
# DB_TYPE=postgresql
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=hrm_system
# DB_USER=your_username
# DB_PASSWORD=your_password

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your-refresh-token-secret-change-this
JWT_REFRESH_EXPIRES_IN=7d

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET=your-session-secret-change-this

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Configuration
MAX_FILE_SIZE=5242880
UPLOAD_PATH=./uploads

# Email Configuration (Optional)
EMAIL_SERVICE=gmail
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# Company Configuration
COMPANY_NAME=Your Company Name
COMPANY_EMAIL=<EMAIL>
COMPANY_PHONE=******-567-8900
COMPANY_ADDRESS=123 Business St, City, State 12345

# Feature Flags
ENABLE_GPS_TRACKING=false
ENABLE_PHOTO_VERIFICATION=false
ENABLE_EMAIL_NOTIFICATIONS=true
ENABLE_SMS_NOTIFICATIONS=false

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_INTERVAL=24h
BACKUP_RETENTION_DAYS=30
