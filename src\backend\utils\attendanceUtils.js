const database = require('../config/database');
const Helpers = require('./helpers');

class AttendanceUtils {
  // Calculate attendance percentage
  static calculateAttendancePercentage(presentDays, totalWorkingDays) {
    if (totalWorkingDays === 0) return 0;
    return Math.round((presentDays / totalWorkingDays) * 100);
  }

  // Get working days in a month (excluding weekends)
  static getWorkingDaysInMonth(year, month) {
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    let workingDays = 0;

    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      const dayOfWeek = date.getDay();
      // Exclude weekends (0 = Sunday, 6 = Saturday)
      if (dayOfWeek !== 0 && dayOfWeek !== 6) {
        workingDays++;
      }
    }

    return workingDays;
  }

  // Determine attendance status based on clock in time
  static determineAttendanceStatus(clockInTime, standardStartTime = '09:00:00') {
    if (!clockInTime) return 'absent';

    const clockIn = new Date(`1970-01-01T${clockInTime}`);
    const standardStart = new Date(`1970-01-01T${standardStartTime}`);
    
    // Late threshold (15 minutes)
    const lateThreshold = new Date(standardStart.getTime() + 15 * 60 * 1000);

    if (clockIn <= standardStart) {
      return 'present';
    } else if (clockIn <= lateThreshold) {
      return 'present'; // Within grace period
    } else {
      return 'late';
    }
  }

  // Calculate overtime hours
  static calculateOvertime(totalHours, standardHours = 8) {
    return Math.max(0, totalHours - standardHours);
  }

  // Generate monthly attendance report for employee
  static async generateMonthlyReport(employeeId, year, month) {
    const startDate = `${year}-${String(month + 1).padStart(2, '0')}-01`;
    const endDate = new Date(year, month + 1, 0).toISOString().split('T')[0];

    const attendanceRecords = await database.all(`
      SELECT * FROM attendance 
      WHERE employee_id = ? AND date BETWEEN ? AND ?
      ORDER BY date
    `, [employeeId, startDate, endDate]);

    const workingDaysInMonth = this.getWorkingDaysInMonth(year, month);
    
    const summary = {
      employee_id: employeeId,
      year,
      month: month + 1,
      working_days_in_month: workingDaysInMonth,
      total_present: 0,
      total_absent: 0,
      total_late: 0,
      total_half_days: 0,
      total_hours: 0,
      total_overtime: 0,
      attendance_percentage: 0,
      records: attendanceRecords
    };

    attendanceRecords.forEach(record => {
      switch (record.status) {
        case 'present':
          summary.total_present++;
          break;
        case 'absent':
          summary.total_absent++;
          break;
        case 'late':
          summary.total_late++;
          break;
        case 'half_day':
          summary.total_half_days++;
          break;
      }

      if (record.total_hours) {
        summary.total_hours += record.total_hours;
      }
      if (record.overtime_hours) {
        summary.total_overtime += record.overtime_hours;
      }
    });

    summary.attendance_percentage = this.calculateAttendancePercentage(
      summary.total_present + summary.total_late + summary.total_half_days,
      workingDaysInMonth
    );

    return summary;
  }

  // Generate department attendance summary
  static async generateDepartmentSummary(department, startDate, endDate) {
    const summary = await database.get(`
      SELECT 
        e.department,
        COUNT(DISTINCT a.employee_id) as total_employees,
        COUNT(a.id) as total_attendance_records,
        SUM(CASE WHEN a.status = 'present' THEN 1 ELSE 0 END) as total_present,
        SUM(CASE WHEN a.status = 'absent' THEN 1 ELSE 0 END) as total_absent,
        SUM(CASE WHEN a.status = 'late' THEN 1 ELSE 0 END) as total_late,
        SUM(CASE WHEN a.status = 'half_day' THEN 1 ELSE 0 END) as total_half_days,
        AVG(a.total_hours) as avg_hours_per_day,
        SUM(a.overtime_hours) as total_overtime_hours,
        AVG(a.break_duration) as avg_break_minutes
      FROM attendance a
      LEFT JOIN employees e ON a.employee_id = e.id
      WHERE e.department = ? AND a.date BETWEEN ? AND ?
      GROUP BY e.department
    `, [department, startDate, endDate]);

    // Calculate working days in the period
    const workingDays = Helpers.calculateWorkingDays(startDate, endDate);
    const expectedAttendanceRecords = summary.total_employees * workingDays;

    if (summary) {
      summary.working_days_in_period = workingDays;
      summary.expected_attendance_records = expectedAttendanceRecords;
      summary.attendance_percentage = this.calculateAttendancePercentage(
        summary.total_present + summary.total_late + summary.total_half_days,
        expectedAttendanceRecords
      );
    }

    return summary;
  }

  // Get attendance trends (daily, weekly, monthly)
  static async getAttendanceTrends(filters = {}) {
    const { start_date, end_date, department, employee_id } = filters;

    let sql = `
      SELECT 
        DATE(a.date) as date,
        COUNT(a.id) as total_records,
        SUM(CASE WHEN a.status = 'present' THEN 1 ELSE 0 END) as present_count,
        SUM(CASE WHEN a.status = 'absent' THEN 1 ELSE 0 END) as absent_count,
        SUM(CASE WHEN a.status = 'late' THEN 1 ELSE 0 END) as late_count,
        AVG(a.total_hours) as avg_hours
      FROM attendance a
      LEFT JOIN employees e ON a.employee_id = e.id
      WHERE 1=1
    `;
    const params = [];

    if (start_date) {
      sql += ' AND a.date >= ?';
      params.push(start_date);
    }

    if (end_date) {
      sql += ' AND a.date <= ?';
      params.push(end_date);
    }

    if (department) {
      sql += ' AND e.department = ?';
      params.push(department);
    }

    if (employee_id) {
      sql += ' AND a.employee_id = ?';
      params.push(employee_id);
    }

    sql += ' GROUP BY DATE(a.date) ORDER BY a.date';

    return await database.all(sql, params);
  }

  // Identify attendance patterns and anomalies
  static async identifyAttendancePatterns(employeeId, days = 30) {
    const endDate = new Date().toISOString().split('T')[0];
    const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    const records = await database.all(`
      SELECT * FROM attendance 
      WHERE employee_id = ? AND date BETWEEN ? AND ?
      ORDER BY date
    `, [employeeId, startDate, endDate]);

    const patterns = {
      employee_id: employeeId,
      analysis_period: { start_date: startDate, end_date: endDate },
      total_days: records.length,
      patterns: {
        frequent_late_arrivals: 0,
        early_departures: 0,
        long_breaks: 0,
        overtime_frequency: 0,
        absent_days: 0
      },
      averages: {
        clock_in_time: null,
        clock_out_time: null,
        total_hours: 0,
        break_duration: 0
      },
      anomalies: []
    };

    let totalClockInMinutes = 0;
    let totalClockOutMinutes = 0;
    let totalHours = 0;
    let totalBreakMinutes = 0;
    let validClockInCount = 0;
    let validClockOutCount = 0;

    records.forEach(record => {
      // Count patterns
      if (record.status === 'late') {
        patterns.patterns.frequent_late_arrivals++;
      }
      if (record.status === 'absent') {
        patterns.patterns.absent_days++;
      }
      if (record.overtime_hours > 0) {
        patterns.patterns.overtime_frequency++;
      }
      if (record.break_duration > 60) { // More than 1 hour break
        patterns.patterns.long_breaks++;
      }

      // Calculate averages
      if (record.clock_in) {
        totalClockInMinutes += Helpers.timeToMinutes(record.clock_in);
        validClockInCount++;
      }
      if (record.clock_out) {
        totalClockOutMinutes += Helpers.timeToMinutes(record.clock_out);
        validClockOutCount++;
        
        // Check for early departure (before 5 PM)
        if (Helpers.timeToMinutes(record.clock_out) < Helpers.timeToMinutes('17:00:00')) {
          patterns.patterns.early_departures++;
        }
      }
      if (record.total_hours) {
        totalHours += record.total_hours;
      }
      if (record.break_duration) {
        totalBreakMinutes += record.break_duration;
      }

      // Identify anomalies
      if (record.total_hours > 12) {
        patterns.anomalies.push({
          date: record.date,
          type: 'excessive_hours',
          value: record.total_hours,
          description: 'Worked more than 12 hours'
        });
      }
      if (record.break_duration > 120) { // More than 2 hours break
        patterns.anomalies.push({
          date: record.date,
          type: 'long_break',
          value: record.break_duration,
          description: 'Break longer than 2 hours'
        });
      }
    });

    // Calculate averages
    if (validClockInCount > 0) {
      patterns.averages.clock_in_time = Helpers.minutesToTime(totalClockInMinutes / validClockInCount);
    }
    if (validClockOutCount > 0) {
      patterns.averages.clock_out_time = Helpers.minutesToTime(totalClockOutMinutes / validClockOutCount);
    }
    if (records.length > 0) {
      patterns.averages.total_hours = Math.round((totalHours / records.length) * 100) / 100;
      patterns.averages.break_duration = Math.round(totalBreakMinutes / records.length);
    }

    return patterns;
  }

  // Generate attendance alerts
  static async generateAttendanceAlerts(date = null) {
    const targetDate = date || new Date().toISOString().split('T')[0];
    
    const alerts = {
      date: targetDate,
      alerts: []
    };

    // Late arrivals
    const lateArrivals = await database.all(`
      SELECT a.*, e.first_name, e.last_name, e.employee_id as emp_id
      FROM attendance a
      LEFT JOIN employees e ON a.employee_id = e.id
      WHERE a.date = ? AND a.status = 'late'
    `, [targetDate]);

    lateArrivals.forEach(record => {
      alerts.alerts.push({
        type: 'late_arrival',
        employee_id: record.employee_id,
        employee_name: `${record.first_name} ${record.last_name}`,
        employee_code: record.emp_id,
        time: record.clock_in,
        severity: 'medium'
      });
    });

    // Absent employees
    const absentEmployees = await database.all(`
      SELECT a.*, e.first_name, e.last_name, e.employee_id as emp_id
      FROM attendance a
      LEFT JOIN employees e ON a.employee_id = e.id
      WHERE a.date = ? AND a.status = 'absent'
    `, [targetDate]);

    absentEmployees.forEach(record => {
      alerts.alerts.push({
        type: 'absent',
        employee_id: record.employee_id,
        employee_name: `${record.first_name} ${record.last_name}`,
        employee_code: record.emp_id,
        severity: 'high'
      });
    });

    // Employees who haven't clocked out (after 6 PM)
    const currentTime = new Date().toTimeString().split(' ')[0];
    if (currentTime > '18:00:00') {
      const notClockedOut = await database.all(`
        SELECT a.*, e.first_name, e.last_name, e.employee_id as emp_id
        FROM attendance a
        LEFT JOIN employees e ON a.employee_id = e.id
        WHERE a.date = ? AND a.clock_in IS NOT NULL AND a.clock_out IS NULL
      `, [targetDate]);

      notClockedOut.forEach(record => {
        alerts.alerts.push({
          type: 'not_clocked_out',
          employee_id: record.employee_id,
          employee_name: `${record.first_name} ${record.last_name}`,
          employee_code: record.emp_id,
          clock_in_time: record.clock_in,
          severity: 'medium'
        });
      });
    }

    return alerts;
  }

  // Calculate payroll hours (regular + overtime)
  static calculatePayrollHours(attendanceRecords) {
    let regularHours = 0;
    let overtimeHours = 0;

    attendanceRecords.forEach(record => {
      if (record.total_hours) {
        const workingHours = record.total_hours - (record.break_duration || 0) / 60;
        const standardHours = 8;
        
        if (workingHours <= standardHours) {
          regularHours += workingHours;
        } else {
          regularHours += standardHours;
          overtimeHours += workingHours - standardHours;
        }
      }
    });

    return {
      regular_hours: Math.round(regularHours * 100) / 100,
      overtime_hours: Math.round(overtimeHours * 100) / 100,
      total_hours: Math.round((regularHours + overtimeHours) * 100) / 100
    };
  }
}

module.exports = AttendanceUtils;
