const express = require('express');
const router = express.Router();
const DepartmentController = require('../controllers/departmentController');
const { authenticate, authorize, authorizeMinRole, auditLog } = require('../middleware/auth');
const { validateId } = require('../middleware/validation');
const { body } = require('express-validator');

// Department validation
const validateDepartmentCreate = [
  body('name')
    .notEmpty()
    .withMessage('Department name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Department name must be between 2 and 100 characters')
    .matches(/^[a-zA-Z\s&-]+$/)
    .withMessage('Department name can only contain letters, spaces, ampersands, and hyphens'),
  
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description cannot exceed 500 characters'),
  
  body('manager_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Manager ID must be a positive integer'),
  
  body('budget')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Budget must be a positive number')
];

const validateDepartmentUpdate = [
  body('name')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Department name must be between 2 and 100 characters')
    .matches(/^[a-zA-Z\s&-]+$/)
    .withMessage('Department name can only contain letters, spaces, ampersands, and hyphens'),
  
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description cannot exceed 500 characters'),
  
  body('manager_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Manager ID must be a positive integer'),
  
  body('budget')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Budget must be a positive number')
];

/**
 * @route   GET /api/departments
 * @desc    Get all departments
 * @access  Private
 */
router.get('/',
  authenticate,
  DepartmentController.getAllDepartments
);

/**
 * @route   GET /api/departments/managers
 * @desc    Get available managers for departments
 * @access  Private (HR/Admin only)
 */
router.get('/managers',
  authenticate,
  authorizeMinRole('hr'),
  DepartmentController.getAvailableManagers
);

/**
 * @route   POST /api/departments
 * @desc    Create new department
 * @access  Private (HR/Admin only)
 */
router.post('/',
  authenticate,
  authorizeMinRole('hr'),
  validateDepartmentCreate,
  auditLog('DEPARTMENT_CREATE'),
  DepartmentController.createDepartment
);

/**
 * @route   GET /api/departments/:id
 * @desc    Get department by ID
 * @access  Private
 */
router.get('/:id',
  authenticate,
  validateId,
  DepartmentController.getDepartmentById
);

/**
 * @route   PUT /api/departments/:id
 * @desc    Update department
 * @access  Private (HR/Admin only)
 */
router.put('/:id',
  authenticate,
  authorizeMinRole('hr'),
  validateId,
  validateDepartmentUpdate,
  auditLog('DEPARTMENT_UPDATE'),
  DepartmentController.updateDepartment
);

/**
 * @route   DELETE /api/departments/:id
 * @desc    Delete department
 * @access  Private (Admin only)
 */
router.delete('/:id',
  authenticate,
  authorize('admin'),
  validateId,
  auditLog('DEPARTMENT_DELETE'),
  DepartmentController.deleteDepartment
);

/**
 * @route   GET /api/departments/:id/employees
 * @desc    Get department employees
 * @access  Private
 */
router.get('/:id/employees',
  authenticate,
  validateId,
  DepartmentController.getDepartmentEmployees
);

/**
 * @route   GET /api/departments/:id/stats
 * @desc    Get department statistics
 * @access  Private (HR/Admin/Manager)
 */
router.get('/:id/stats',
  authenticate,
  authorizeMinRole('manager'),
  validateId,
  DepartmentController.getDepartmentStats
);

// Error handling middleware for department routes
router.use((error, req, res, next) => {
  console.error('Department route error:', error);
  
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: error.errors
    });
  }
  
  if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
    return res.status(409).json({
      success: false,
      message: 'Department name already exists'
    });
  }
  
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? error.message : undefined
  });
});

module.exports = router;
