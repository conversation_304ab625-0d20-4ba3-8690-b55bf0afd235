const Department = require('../models/Department');
const Employee = require('../models/Employee');
const database = require('../config/database');
const { validationResult } = require('express-validator');

class DepartmentController {
  // Get all departments
  static async getAllDepartments(req, res) {
    try {
      const departments = await database.all(`
        SELECT d.*, 
               e.first_name as manager_first_name, 
               e.last_name as manager_last_name,
               e.employee_id as manager_employee_id,
               COUNT(emp.id) as employee_count
        FROM departments d
        LEFT JOIN employees e ON d.manager_id = e.id
        LEFT JOIN employees emp ON emp.department = d.name AND emp.status = 'active'
        GROUP BY d.id
        ORDER BY d.name
      `);

      res.json({
        success: true,
        data: { departments }
      });

    } catch (error) {
      console.error('Get departments error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Get department by ID
  static async getDepartmentById(req, res) {
    try {
      const { id } = req.params;
      
      const department = await Department.findById(id);
      if (!department) {
        return res.status(404).json({
          success: false,
          message: 'Department not found'
        });
      }

      const departmentWithDetails = await department.getWithManager();
      const employees = await department.getEmployees();

      res.json({
        success: true,
        data: { 
          department: departmentWithDetails,
          employees
        }
      });

    } catch (error) {
      console.error('Get department error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Create new department
  static async createDepartment(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const departmentData = req.body;

      // Validate department data
      const validationErrors = Department.validate(departmentData);
      if (validationErrors.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: validationErrors
        });
      }

      // Check if department name already exists
      const existingDepartment = await Department.findByName(departmentData.name);
      if (existingDepartment) {
        return res.status(409).json({
          success: false,
          message: 'Department name already exists'
        });
      }

      // Validate manager if provided
      if (departmentData.manager_id) {
        const manager = await Employee.findById(departmentData.manager_id);
        if (!manager) {
          return res.status(400).json({
            success: false,
            message: 'Invalid manager ID'
          });
        }
      }

      // Create department
      const department = await Department.create(departmentData);

      // Get department with details
      const departmentWithDetails = await department.getWithManager();

      // Log audit
      await database.run(`
        INSERT INTO audit_logs (user_id, action, table_name, record_id, new_values, ip_address, user_agent)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        req.user.id,
        'DEPARTMENT_CREATE',
        'departments',
        department.id,
        JSON.stringify(departmentData),
        req.ip,
        req.get('User-Agent')
      ]);

      res.status(201).json({
        success: true,
        message: 'Department created successfully',
        data: { department: departmentWithDetails }
      });

    } catch (error) {
      console.error('Create department error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Update department
  static async updateDepartment(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { id } = req.params;
      const updateData = req.body;

      const department = await Department.findById(id);
      if (!department) {
        return res.status(404).json({
          success: false,
          message: 'Department not found'
        });
      }

      // Check if new name conflicts with existing department
      if (updateData.name && updateData.name !== department.name) {
        const existingDepartment = await Department.findByName(updateData.name);
        if (existingDepartment) {
          return res.status(409).json({
            success: false,
            message: 'Department name already exists'
          });
        }
      }

      // Validate manager if provided
      if (updateData.manager_id) {
        const manager = await Employee.findById(updateData.manager_id);
        if (!manager) {
          return res.status(400).json({
            success: false,
            message: 'Invalid manager ID'
          });
        }
      }

      // Store old values for audit
      const oldValues = { ...department };

      // Update department
      await department.update(updateData);

      // If department name changed, update all employees
      if (updateData.name && updateData.name !== oldValues.name) {
        await database.run(
          'UPDATE employees SET department = ? WHERE department = ?',
          [updateData.name, oldValues.name]
        );
      }

      // Get updated department with details
      const updatedDepartment = await department.getWithManager();

      // Log audit
      await database.run(`
        INSERT INTO audit_logs (user_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        req.user.id,
        'DEPARTMENT_UPDATE',
        'departments',
        department.id,
        JSON.stringify(oldValues),
        JSON.stringify(updateData),
        req.ip,
        req.get('User-Agent')
      ]);

      res.json({
        success: true,
        message: 'Department updated successfully',
        data: { department: updatedDepartment }
      });

    } catch (error) {
      console.error('Update department error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Delete department
  static async deleteDepartment(req, res) {
    try {
      const { id } = req.params;
      
      const department = await Department.findById(id);
      if (!department) {
        return res.status(404).json({
          success: false,
          message: 'Department not found'
        });
      }

      // Check if department has employees
      const employeeCount = await department.getEmployeeCount();
      if (employeeCount > 0) {
        return res.status(400).json({
          success: false,
          message: 'Cannot delete department with active employees'
        });
      }

      await department.delete();

      // Log audit
      await database.run(`
        INSERT INTO audit_logs (user_id, action, table_name, record_id, ip_address, user_agent)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [
        req.user.id,
        'DEPARTMENT_DELETE',
        'departments',
        department.id,
        req.ip,
        req.get('User-Agent')
      ]);

      res.json({
        success: true,
        message: 'Department deleted successfully'
      });

    } catch (error) {
      console.error('Delete department error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Get department employees
  static async getDepartmentEmployees(req, res) {
    try {
      const { id } = req.params;
      
      const department = await Department.findById(id);
      if (!department) {
        return res.status(404).json({
          success: false,
          message: 'Department not found'
        });
      }

      const employees = await database.all(`
        SELECT e.*, u.username, u.email as user_email, u.role
        FROM employees e
        LEFT JOIN users u ON e.user_id = u.id
        WHERE e.department = ? AND e.status = 'active'
        ORDER BY e.first_name, e.last_name
      `, [department.name]);

      res.json({
        success: true,
        data: { employees }
      });

    } catch (error) {
      console.error('Get department employees error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Get department statistics
  static async getDepartmentStats(req, res) {
    try {
      const { id } = req.params;
      
      const department = await Department.findById(id);
      if (!department) {
        return res.status(404).json({
          success: false,
          message: 'Department not found'
        });
      }

      const stats = await database.get(`
        SELECT 
          COUNT(*) as total_employees,
          SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_employees,
          AVG(salary) as avg_salary,
          MIN(salary) as min_salary,
          MAX(salary) as max_salary,
          COUNT(DISTINCT position) as total_positions
        FROM employees
        WHERE department = ?
      `, [department.name]);

      const positionBreakdown = await database.all(`
        SELECT 
          position,
          COUNT(*) as employee_count,
          AVG(salary) as avg_salary
        FROM employees 
        WHERE department = ? AND status = 'active' AND position IS NOT NULL
        GROUP BY position
        ORDER BY employee_count DESC
      `, [department.name]);

      res.json({
        success: true,
        data: {
          department: department.toJSON(),
          stats,
          positions: positionBreakdown
        }
      });

    } catch (error) {
      console.error('Get department stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Get available managers for department
  static async getAvailableManagers(req, res) {
    try {
      const managers = await database.all(`
        SELECT e.id, e.employee_id, e.first_name, e.last_name, e.department, e.position
        FROM employees e
        LEFT JOIN users u ON e.user_id = u.id
        WHERE e.status = 'active' 
        AND (u.role = 'manager' OR u.role = 'hr' OR u.role = 'admin')
        ORDER BY e.first_name, e.last_name
      `);

      res.json({
        success: true,
        data: { managers }
      });

    } catch (error) {
      console.error('Get available managers error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }
}

module.exports = DepartmentController;
