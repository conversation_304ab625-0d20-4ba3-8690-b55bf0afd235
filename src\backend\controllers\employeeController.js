const Employee = require('../models/Employee');
const User = require('../models/User');
const Department = require('../models/Department');
const database = require('../config/database');
const { validationResult } = require('express-validator');

class EmployeeController {
  // Get all employees
  static async getAllEmployees(req, res) {
    try {
      const {
        department,
        status = 'active',
        manager_id,
        search,
        page = 1,
        limit = 50
      } = req.query;

      // Build filters based on user role
      const filters = {};
      
      if (department) filters.department = department;
      if (status) filters.status = status;
      
      // Role-based filtering
      if (req.user.role === 'manager') {
        // Managers can only see their subordinates and themselves
        const managerEmployee = await Employee.findByUserId(req.user.id);
        if (managerEmployee) {
          filters.manager_id = managerEmployee.id;
        }
      }
      
      if (manager_id) filters.manager_id = manager_id;

      // Get employees with pagination
      let sql = `
        SELECT e.*, u.username, u.email as user_email, u.role, u.is_active, u.last_login,
               m.first_name as manager_first_name, m.last_name as manager_last_name
        FROM employees e
        LEFT JOIN users u ON e.user_id = u.id
        LEFT JOIN employees m ON e.manager_id = m.id
        WHERE 1=1
      `;
      const params = [];

      // Apply filters
      if (filters.department) {
        sql += ' AND e.department = ?';
        params.push(filters.department);
      }

      if (filters.status) {
        sql += ' AND e.status = ?';
        params.push(filters.status);
      }

      if (filters.manager_id) {
        sql += ' AND e.manager_id = ?';
        params.push(filters.manager_id);
      }

      // Search functionality
      if (search) {
        sql += ` AND (
          e.first_name LIKE ? OR 
          e.last_name LIKE ? OR 
          e.employee_id LIKE ? OR 
          e.email LIKE ? OR
          e.department LIKE ? OR
          e.position LIKE ?
        )`;
        const searchTerm = `%${search}%`;
        params.push(searchTerm, searchTerm, searchTerm, searchTerm, searchTerm, searchTerm);
      }

      // Add ordering
      sql += ' ORDER BY e.first_name, e.last_name';

      // Add pagination
      const offset = (page - 1) * limit;
      sql += ' LIMIT ? OFFSET ?';
      params.push(parseInt(limit), parseInt(offset));

      const employees = await database.all(sql, params);

      // Get total count for pagination
      let countSql = 'SELECT COUNT(*) as total FROM employees e WHERE 1=1';
      const countParams = [];

      if (filters.department) {
        countSql += ' AND e.department = ?';
        countParams.push(filters.department);
      }

      if (filters.status) {
        countSql += ' AND e.status = ?';
        countParams.push(filters.status);
      }

      if (filters.manager_id) {
        countSql += ' AND e.manager_id = ?';
        countParams.push(filters.manager_id);
      }

      if (search) {
        countSql += ` AND (
          e.first_name LIKE ? OR 
          e.last_name LIKE ? OR 
          e.employee_id LIKE ? OR 
          e.email LIKE ? OR
          e.department LIKE ? OR
          e.position LIKE ?
        )`;
        const searchTerm = `%${search}%`;
        countParams.push(searchTerm, searchTerm, searchTerm, searchTerm, searchTerm, searchTerm);
      }

      const { total } = await database.get(countSql, countParams);

      res.json({
        success: true,
        data: {
          employees,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / limit)
          }
        }
      });

    } catch (error) {
      console.error('Get employees error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Get employee by ID
  static async getEmployeeById(req, res) {
    try {
      const { id } = req.params;
      
      const employee = await Employee.findById(id);
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: 'Employee not found'
        });
      }

      // Check permissions
      if (req.user.role === 'employee') {
        const userEmployee = await Employee.findByUserId(req.user.id);
        if (!userEmployee || userEmployee.id !== parseInt(id)) {
          return res.status(403).json({
            success: false,
            message: 'Access denied'
          });
        }
      }

      const employeeWithDetails = await employee.getWithUser();

      res.json({
        success: true,
        data: { employee: employeeWithDetails }
      });

    } catch (error) {
      console.error('Get employee error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Create new employee
  static async createEmployee(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const employeeData = req.body;

      // Validate employee data
      const validationErrors = Employee.validate(employeeData);
      if (validationErrors.length > 0) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: validationErrors
        });
      }

      // Check if employee ID already exists
      const existingEmployee = await Employee.findByEmployeeId(employeeData.employee_id);
      if (existingEmployee) {
        return res.status(409).json({
          success: false,
          message: 'Employee ID already exists'
        });
      }

      // Create user account if provided
      let user_id = employeeData.user_id;
      if (!user_id && employeeData.create_user_account) {
        const userData = {
          username: employeeData.employee_id.toLowerCase(),
          email: employeeData.email,
          password: employeeData.password || 'password123',
          role: employeeData.role || 'employee'
        };

        const user = await User.create(userData);
        user_id = user.id;
      }

      // Create employee
      const employee = await Employee.create({
        ...employeeData,
        user_id
      });

      // Get employee with details
      const employeeWithDetails = await employee.getWithUser();

      // Log audit
      await database.run(`
        INSERT INTO audit_logs (user_id, action, table_name, record_id, new_values, ip_address, user_agent)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        req.user.id,
        'EMPLOYEE_CREATE',
        'employees',
        employee.id,
        JSON.stringify(employeeData),
        req.ip,
        req.get('User-Agent')
      ]);

      res.status(201).json({
        success: true,
        message: 'Employee created successfully',
        data: { employee: employeeWithDetails }
      });

    } catch (error) {
      console.error('Create employee error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Update employee
  static async updateEmployee(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { id } = req.params;
      const updateData = req.body;

      const employee = await Employee.findById(id);
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: 'Employee not found'
        });
      }

      // Check permissions
      if (req.user.role === 'manager') {
        const managerEmployee = await Employee.findByUserId(req.user.id);
        if (!managerEmployee || employee.manager_id !== managerEmployee.id) {
          return res.status(403).json({
            success: false,
            message: 'Access denied'
          });
        }
      }

      // Store old values for audit
      const oldValues = { ...employee };

      // Update employee
      await employee.update(updateData);

      // Get updated employee with details
      const updatedEmployee = await employee.getWithUser();

      // Log audit
      await database.run(`
        INSERT INTO audit_logs (user_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        req.user.id,
        'EMPLOYEE_UPDATE',
        'employees',
        employee.id,
        JSON.stringify(oldValues),
        JSON.stringify(updateData),
        req.ip,
        req.get('User-Agent')
      ]);

      res.json({
        success: true,
        message: 'Employee updated successfully',
        data: { employee: updatedEmployee }
      });

    } catch (error) {
      console.error('Update employee error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Delete/Deactivate employee
  static async deleteEmployee(req, res) {
    try {
      const { id } = req.params;
      
      const employee = await Employee.findById(id);
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: 'Employee not found'
        });
      }

      // Deactivate instead of hard delete
      await employee.deactivate();

      // Also deactivate user account if exists
      if (employee.user_id) {
        const user = await User.findById(employee.user_id);
        if (user) {
          await user.deactivate();
        }
      }

      // Log audit
      await database.run(`
        INSERT INTO audit_logs (user_id, action, table_name, record_id, ip_address, user_agent)
        VALUES (?, ?, ?, ?, ?, ?)
      `, [
        req.user.id,
        'EMPLOYEE_DELETE',
        'employees',
        employee.id,
        req.ip,
        req.get('User-Agent')
      ]);

      res.json({
        success: true,
        message: 'Employee deactivated successfully'
      });

    } catch (error) {
      console.error('Delete employee error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Get employee's subordinates
  static async getSubordinates(req, res) {
    try {
      const { id } = req.params;
      
      const employee = await Employee.findById(id);
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: 'Employee not found'
        });
      }

      const subordinates = await employee.getSubordinates();

      res.json({
        success: true,
        data: { subordinates }
      });

    } catch (error) {
      console.error('Get subordinates error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Get employee's manager
  static async getManager(req, res) {
    try {
      const { id } = req.params;
      
      const employee = await Employee.findById(id);
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: 'Employee not found'
        });
      }

      const manager = await employee.getManager();

      res.json({
        success: true,
        data: { manager }
      });

    } catch (error) {
      console.error('Get manager error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Get employee statistics
  static async getEmployeeStats(req, res) {
    try {
      const stats = await database.get(`
        SELECT 
          COUNT(*) as total_employees,
          SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_employees,
          SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_employees,
          SUM(CASE WHEN status = 'terminated' THEN 1 ELSE 0 END) as terminated_employees,
          COUNT(DISTINCT department) as total_departments
        FROM employees
      `);

      const departmentStats = await database.all(`
        SELECT 
          department,
          COUNT(*) as employee_count,
          AVG(salary) as avg_salary
        FROM employees 
        WHERE status = 'active' AND department IS NOT NULL
        GROUP BY department
        ORDER BY employee_count DESC
      `);

      res.json({
        success: true,
        data: {
          overview: stats,
          departments: departmentStats
        }
      });

    } catch (error) {
      console.error('Get employee stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }
}

module.exports = EmployeeController;
