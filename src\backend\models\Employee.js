const database = require('../config/database');

class Employee {
  constructor(data = {}) {
    this.id = data.id;
    this.user_id = data.user_id;
    this.employee_id = data.employee_id;
    this.first_name = data.first_name;
    this.last_name = data.last_name;
    this.email = data.email;
    this.phone = data.phone;
    this.department = data.department;
    this.position = data.position;
    this.hire_date = data.hire_date;
    this.salary = data.salary;
    this.manager_id = data.manager_id;
    this.status = data.status;
    this.address = data.address;
    this.date_of_birth = data.date_of_birth;
    this.emergency_contact_name = data.emergency_contact_name;
    this.emergency_contact_phone = data.emergency_contact_phone;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  // Create new employee
  static async create(employeeData) {
    const {
      user_id, employee_id, first_name, last_name, email, phone,
      department, position, hire_date, salary, manager_id, status = 'active',
      address, date_of_birth, emergency_contact_name, emergency_contact_phone
    } = employeeData;

    const sql = `
      INSERT INTO employees (
        user_id, employee_id, first_name, last_name, email, phone,
        department, position, hire_date, salary, manager_id, status,
        address, date_of_birth, emergency_contact_name, emergency_contact_phone
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await database.run(sql, [
      user_id, employee_id, first_name, last_name, email, phone,
      department, position, hire_date, salary, manager_id, status,
      address, date_of_birth, emergency_contact_name, emergency_contact_phone
    ]);

    return await Employee.findById(result.id);
  }

  // Find employee by ID
  static async findById(id) {
    const sql = 'SELECT * FROM employees WHERE id = ?';
    const row = await database.get(sql, [id]);
    return row ? new Employee(row) : null;
  }

  // Find employee by employee ID
  static async findByEmployeeId(employeeId) {
    const sql = 'SELECT * FROM employees WHERE employee_id = ?';
    const row = await database.get(sql, [employeeId]);
    return row ? new Employee(row) : null;
  }

  // Find employee by user ID
  static async findByUserId(userId) {
    const sql = 'SELECT * FROM employees WHERE user_id = ?';
    const row = await database.get(sql, [userId]);
    return row ? new Employee(row) : null;
  }

  // Get all employees
  static async findAll(filters = {}) {
    let sql = 'SELECT * FROM employees WHERE 1=1';
    const params = [];

    if (filters.department) {
      sql += ' AND department = ?';
      params.push(filters.department);
    }

    if (filters.status) {
      sql += ' AND status = ?';
      params.push(filters.status);
    }

    if (filters.manager_id) {
      sql += ' AND manager_id = ?';
      params.push(filters.manager_id);
    }

    sql += ' ORDER BY first_name, last_name';

    const rows = await database.all(sql, params);
    return rows.map(row => new Employee(row));
  }

  // Get employee with user details
  async getWithUser() {
    const sql = `
      SELECT e.*, u.username, u.email as user_email, u.role, u.is_active, u.last_login
      FROM employees e
      LEFT JOIN users u ON e.user_id = u.id
      WHERE e.id = ?
    `;
    const row = await database.get(sql, [this.id]);
    return row;
  }

  // Get employee's manager
  async getManager() {
    if (!this.manager_id) return null;
    return await Employee.findById(this.manager_id);
  }

  // Get employee's subordinates
  async getSubordinates() {
    const sql = 'SELECT * FROM employees WHERE manager_id = ? AND status = "active"';
    const rows = await database.all(sql, [this.id]);
    return rows.map(row => new Employee(row));
  }

  // Update employee
  async update(updateData) {
    const allowedFields = [
      'first_name', 'last_name', 'email', 'phone', 'department', 'position',
      'hire_date', 'salary', 'manager_id', 'status', 'address', 'date_of_birth',
      'emergency_contact_name', 'emergency_contact_phone'
    ];
    
    const updates = [];
    const params = [];

    for (const [key, value] of Object.entries(updateData)) {
      if (allowedFields.includes(key)) {
        updates.push(`${key} = ?`);
        params.push(value);
      }
    }

    if (updates.length === 0) {
      throw new Error('No valid fields to update');
    }

    updates.push('updated_at = CURRENT_TIMESTAMP');
    params.push(this.id);

    const sql = `UPDATE employees SET ${updates.join(', ')} WHERE id = ?`;
    await database.run(sql, params);

    // Refresh the instance
    const updated = await Employee.findById(this.id);
    Object.assign(this, updated);
    return this;
  }

  // Get employee's attendance records
  async getAttendanceRecords(startDate = null, endDate = null) {
    let sql = 'SELECT * FROM attendance WHERE employee_id = ?';
    const params = [this.id];

    if (startDate) {
      sql += ' AND date >= ?';
      params.push(startDate);
    }

    if (endDate) {
      sql += ' AND date <= ?';
      params.push(endDate);
    }

    sql += ' ORDER BY date DESC';

    return await database.all(sql, params);
  }

  // Get employee's leave requests
  async getLeaveRequests(status = null) {
    let sql = 'SELECT * FROM leave_requests WHERE employee_id = ?';
    const params = [this.id];

    if (status) {
      sql += ' AND status = ?';
      params.push(status);
    }

    sql += ' ORDER BY created_at DESC';

    return await database.all(sql, params);
  }

  // Get employee's leave balances
  async getLeaveBalances(year = null) {
    let sql = 'SELECT * FROM leave_balances WHERE employee_id = ?';
    const params = [this.id];

    if (year) {
      sql += ' AND year = ?';
      params.push(year);
    } else {
      sql += ' AND year = ?';
      params.push(new Date().getFullYear());
    }

    return await database.all(sql, params);
  }

  // Calculate total working days in a period
  async getWorkingDaysCount(startDate, endDate) {
    const sql = `
      SELECT COUNT(*) as working_days
      FROM attendance
      WHERE employee_id = ? AND date BETWEEN ? AND ? AND status = 'present'
    `;
    const result = await database.get(sql, [this.id, startDate, endDate]);
    return result.working_days || 0;
  }

  // Get attendance summary for a period
  async getAttendanceSummary(startDate, endDate) {
    const sql = `
      SELECT 
        COUNT(*) as total_days,
        SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_days,
        SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_days,
        SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_days,
        SUM(CASE WHEN status = 'half_day' THEN 1 ELSE 0 END) as half_days,
        SUM(total_hours) as total_hours,
        SUM(overtime_hours) as total_overtime
      FROM attendance
      WHERE employee_id = ? AND date BETWEEN ? AND ?
    `;
    return await database.get(sql, [this.id, startDate, endDate]);
  }

  // Deactivate employee
  async deactivate() {
    return await this.update({ status: 'inactive' });
  }

  // Terminate employee
  async terminate() {
    return await this.update({ status: 'terminated' });
  }

  // Reactivate employee
  async reactivate() {
    return await this.update({ status: 'active' });
  }

  // Get full name
  getFullName() {
    return `${this.first_name} ${this.last_name}`;
  }

  // Check if employee is active
  isActive() {
    return this.status === 'active';
  }

  // Get years of service
  getYearsOfService() {
    if (!this.hire_date) return 0;
    const hireDate = new Date(this.hire_date);
    const today = new Date();
    return Math.floor((today - hireDate) / (365.25 * 24 * 60 * 60 * 1000));
  }

  // Validate employee data
  static validate(employeeData) {
    const errors = [];

    if (!employeeData.employee_id) {
      errors.push('Employee ID is required');
    }

    if (!employeeData.first_name || employeeData.first_name.length < 2) {
      errors.push('First name must be at least 2 characters long');
    }

    if (!employeeData.last_name || employeeData.last_name.length < 2) {
      errors.push('Last name must be at least 2 characters long');
    }

    if (employeeData.email && !/\S+@\S+\.\S+/.test(employeeData.email)) {
      errors.push('Valid email format required');
    }

    if (employeeData.salary && (isNaN(employeeData.salary) || employeeData.salary < 0)) {
      errors.push('Salary must be a positive number');
    }

    const validStatuses = ['active', 'inactive', 'terminated'];
    if (employeeData.status && !validStatuses.includes(employeeData.status)) {
      errors.push('Invalid status specified');
    }

    return errors;
  }

  // Convert to JSON
  toJSON() {
    return { ...this };
  }
}

module.exports = Employee;
