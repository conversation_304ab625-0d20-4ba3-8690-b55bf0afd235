const database = require('../config/database');
const Helpers = require('../utils/helpers');

class Attendance {
  constructor(data = {}) {
    this.id = data.id;
    this.employee_id = data.employee_id;
    this.date = data.date;
    this.clock_in = data.clock_in;
    this.clock_out = data.clock_out;
    this.break_start = data.break_start;
    this.break_end = data.break_end;
    this.break_duration = data.break_duration || 0;
    this.total_hours = data.total_hours;
    this.overtime_hours = data.overtime_hours || 0;
    this.status = data.status || 'present';
    this.notes = data.notes;
    this.location_in = data.location_in;
    this.location_out = data.location_out;
    this.ip_address = data.ip_address;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  // Create new attendance record
  static async create(attendanceData) {
    const {
      employee_id, date, clock_in, clock_out, break_start, break_end,
      break_duration = 0, total_hours, overtime_hours = 0, status = 'present',
      notes, location_in, location_out, ip_address
    } = attendanceData;

    const sql = `
      INSERT INTO attendance (
        employee_id, date, clock_in, clock_out, break_start, break_end,
        break_duration, total_hours, overtime_hours, status, notes,
        location_in, location_out, ip_address
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await database.run(sql, [
      employee_id, date, clock_in, clock_out, break_start, break_end,
      break_duration, total_hours, overtime_hours, status, notes,
      location_in, location_out, ip_address
    ]);

    return await Attendance.findById(result.id);
  }

  // Find attendance by ID
  static async findById(id) {
    const sql = 'SELECT * FROM attendance WHERE id = ?';
    const row = await database.get(sql, [id]);
    return row ? new Attendance(row) : null;
  }

  // Find attendance by employee and date
  static async findByEmployeeAndDate(employeeId, date) {
    const sql = 'SELECT * FROM attendance WHERE employee_id = ? AND date = ?';
    const row = await database.get(sql, [employeeId, date]);
    return row ? new Attendance(row) : null;
  }

  // Get attendance records with filters
  static async findAll(filters = {}) {
    let sql = `
      SELECT a.*, e.first_name, e.last_name, e.employee_id as emp_id, e.department
      FROM attendance a
      LEFT JOIN employees e ON a.employee_id = e.id
      WHERE 1=1
    `;
    const params = [];

    if (filters.employee_id) {
      sql += ' AND a.employee_id = ?';
      params.push(filters.employee_id);
    }

    if (filters.department) {
      sql += ' AND e.department = ?';
      params.push(filters.department);
    }

    if (filters.start_date) {
      sql += ' AND a.date >= ?';
      params.push(filters.start_date);
    }

    if (filters.end_date) {
      sql += ' AND a.date <= ?';
      params.push(filters.end_date);
    }

    if (filters.status) {
      sql += ' AND a.status = ?';
      params.push(filters.status);
    }

    if (filters.manager_id) {
      sql += ' AND e.manager_id = ?';
      params.push(filters.manager_id);
    }

    sql += ' ORDER BY a.date DESC, a.clock_in DESC';

    if (filters.limit) {
      sql += ' LIMIT ?';
      params.push(filters.limit);
    }

    if (filters.offset) {
      sql += ' OFFSET ?';
      params.push(filters.offset);
    }

    const rows = await database.all(sql, params);
    return rows.map(row => new Attendance(row));
  }

  // Clock in
  static async clockIn(employeeId, clockInData) {
    const today = Helpers.formatDate(new Date());
    
    // Check if already clocked in today
    const existingRecord = await Attendance.findByEmployeeAndDate(employeeId, today);
    if (existingRecord && existingRecord.clock_in) {
      throw new Error('Already clocked in today');
    }

    const attendanceData = {
      employee_id: employeeId,
      date: today,
      clock_in: clockInData.time || new Date().toTimeString().split(' ')[0],
      location_in: clockInData.location,
      ip_address: clockInData.ip_address,
      status: 'present'
    };

    if (existingRecord) {
      // Update existing record
      return await existingRecord.update(attendanceData);
    } else {
      // Create new record
      return await Attendance.create(attendanceData);
    }
  }

  // Clock out
  async clockOut(clockOutData) {
    if (!this.clock_in) {
      throw new Error('Must clock in before clocking out');
    }

    if (this.clock_out) {
      throw new Error('Already clocked out today');
    }

    const clockOutTime = clockOutData.time || new Date().toTimeString().split(' ')[0];
    const totalHours = Helpers.calculateHours(this.clock_in, clockOutTime);
    
    // Calculate overtime (assuming 8 hours is standard)
    const standardHours = 8;
    const overtimeHours = Math.max(0, totalHours - standardHours);

    const updateData = {
      clock_out: clockOutTime,
      total_hours: totalHours,
      overtime_hours: overtimeHours,
      location_out: clockOutData.location,
      notes: clockOutData.notes
    };

    return await this.update(updateData);
  }

  // Start break
  async startBreak(breakData) {
    if (!this.clock_in) {
      throw new Error('Must clock in before taking a break');
    }

    if (this.break_start && !this.break_end) {
      throw new Error('Break already started');
    }

    const breakStartTime = breakData.time || new Date().toTimeString().split(' ')[0];
    
    return await this.update({
      break_start: breakStartTime
    });
  }

  // End break
  async endBreak(breakData) {
    if (!this.break_start) {
      throw new Error('Must start break before ending it');
    }

    if (this.break_end) {
      throw new Error('Break already ended');
    }

    const breakEndTime = breakData.time || new Date().toTimeString().split(' ')[0];
    const breakDuration = Helpers.calculateHours(this.break_start, breakEndTime) * 60; // Convert to minutes

    return await this.update({
      break_end: breakEndTime,
      break_duration: breakDuration
    });
  }

  // Update attendance record
  async update(updateData) {
    const allowedFields = [
      'clock_in', 'clock_out', 'break_start', 'break_end', 'break_duration',
      'total_hours', 'overtime_hours', 'status', 'notes', 'location_in', 'location_out'
    ];

    const updates = [];
    const params = [];

    for (const [key, value] of Object.entries(updateData)) {
      if (allowedFields.includes(key)) {
        updates.push(`${key} = ?`);
        params.push(value);
      }
    }

    if (updates.length === 0) {
      throw new Error('No valid fields to update');
    }

    updates.push('updated_at = CURRENT_TIMESTAMP');
    params.push(this.id);

    const sql = `UPDATE attendance SET ${updates.join(', ')} WHERE id = ?`;
    await database.run(sql, params);

    // Refresh the instance
    const updated = await Attendance.findById(this.id);
    Object.assign(this, updated);
    return this;
  }

  // Get attendance summary for employee
  static async getEmployeeSummary(employeeId, startDate, endDate) {
    const sql = `
      SELECT 
        COUNT(*) as total_days,
        SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_days,
        SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_days,
        SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_days,
        SUM(CASE WHEN status = 'half_day' THEN 1 ELSE 0 END) as half_days,
        SUM(total_hours) as total_hours,
        SUM(overtime_hours) as total_overtime,
        AVG(total_hours) as avg_hours_per_day,
        SUM(break_duration) as total_break_minutes
      FROM attendance
      WHERE employee_id = ? AND date BETWEEN ? AND ?
    `;
    
    return await database.get(sql, [employeeId, startDate, endDate]);
  }

  // Get department attendance summary
  static async getDepartmentSummary(department, startDate, endDate) {
    const sql = `
      SELECT 
        e.department,
        COUNT(DISTINCT a.employee_id) as total_employees,
        COUNT(a.id) as total_records,
        SUM(CASE WHEN a.status = 'present' THEN 1 ELSE 0 END) as present_count,
        SUM(CASE WHEN a.status = 'absent' THEN 1 ELSE 0 END) as absent_count,
        SUM(CASE WHEN a.status = 'late' THEN 1 ELSE 0 END) as late_count,
        AVG(a.total_hours) as avg_hours,
        SUM(a.overtime_hours) as total_overtime
      FROM attendance a
      LEFT JOIN employees e ON a.employee_id = e.id
      WHERE e.department = ? AND a.date BETWEEN ? AND ?
      GROUP BY e.department
    `;
    
    return await database.get(sql, [department, startDate, endDate]);
  }

  // Get late arrivals
  static async getLateArrivals(date, lateThreshold = '09:00:00') {
    const sql = `
      SELECT a.*, e.first_name, e.last_name, e.employee_id as emp_id, e.department
      FROM attendance a
      LEFT JOIN employees e ON a.employee_id = e.id
      WHERE a.date = ? AND a.clock_in > ? AND a.status != 'absent'
      ORDER BY a.clock_in DESC
    `;
    
    const rows = await database.all(sql, [date, lateThreshold]);
    return rows.map(row => new Attendance(row));
  }

  // Get early departures
  static async getEarlyDepartures(date, earlyThreshold = '17:00:00') {
    const sql = `
      SELECT a.*, e.first_name, e.last_name, e.employee_id as emp_id, e.department
      FROM attendance a
      LEFT JOIN employees e ON a.employee_id = e.id
      WHERE a.date = ? AND a.clock_out < ? AND a.clock_out IS NOT NULL
      ORDER BY a.clock_out ASC
    `;
    
    const rows = await database.all(sql, [date, earlyThreshold]);
    return rows.map(row => new Attendance(row));
  }

  // Mark as absent
  static async markAbsent(employeeId, date, reason = null) {
    const existingRecord = await Attendance.findByEmployeeAndDate(employeeId, date);
    
    if (existingRecord) {
      return await existingRecord.update({
        status: 'absent',
        notes: reason
      });
    } else {
      return await Attendance.create({
        employee_id: employeeId,
        date: date,
        status: 'absent',
        notes: reason
      });
    }
  }

  // Get attendance statistics
  static async getStatistics(filters = {}) {
    let sql = `
      SELECT 
        COUNT(*) as total_records,
        COUNT(DISTINCT employee_id) as unique_employees,
        SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,
        SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,
        SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count,
        SUM(CASE WHEN status = 'half_day' THEN 1 ELSE 0 END) as half_day_count,
        AVG(total_hours) as avg_hours,
        SUM(overtime_hours) as total_overtime,
        AVG(break_duration) as avg_break_minutes
      FROM attendance a
      LEFT JOIN employees e ON a.employee_id = e.id
      WHERE 1=1
    `;
    const params = [];

    if (filters.start_date) {
      sql += ' AND a.date >= ?';
      params.push(filters.start_date);
    }

    if (filters.end_date) {
      sql += ' AND a.date <= ?';
      params.push(filters.end_date);
    }

    if (filters.department) {
      sql += ' AND e.department = ?';
      params.push(filters.department);
    }

    return await database.get(sql, params);
  }

  // Check if employee is currently on break
  isOnBreak() {
    return this.break_start && !this.break_end;
  }

  // Check if employee has clocked in today
  isClockedIn() {
    return !!this.clock_in && !this.clock_out;
  }

  // Check if employee has completed the day
  isCompleted() {
    return !!this.clock_in && !!this.clock_out;
  }

  // Get working hours (excluding break)
  getWorkingHours() {
    if (!this.total_hours) return 0;
    const breakHours = (this.break_duration || 0) / 60;
    return Math.max(0, this.total_hours - breakHours);
  }

  // Validate attendance data
  static validate(attendanceData) {
    const errors = [];

    if (!attendanceData.employee_id) {
      errors.push('Employee ID is required');
    }

    if (!attendanceData.date) {
      errors.push('Date is required');
    }

    if (attendanceData.clock_in && attendanceData.clock_out) {
      const clockIn = new Date(`1970-01-01T${attendanceData.clock_in}`);
      const clockOut = new Date(`1970-01-01T${attendanceData.clock_out}`);
      
      if (clockOut <= clockIn) {
        errors.push('Clock out time must be after clock in time');
      }
    }

    const validStatuses = ['present', 'absent', 'late', 'half_day', 'holiday'];
    if (attendanceData.status && !validStatuses.includes(attendanceData.status)) {
      errors.push('Invalid attendance status');
    }

    return errors;
  }

  // Convert to JSON
  toJSON() {
    return { ...this };
  }
}

module.exports = Attendance;
