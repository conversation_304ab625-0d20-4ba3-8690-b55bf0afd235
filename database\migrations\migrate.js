const fs = require('fs');
const path = require('path');
const database = require('../../src/backend/config/database');

class MigrationRunner {
  constructor() {
    this.migrationsPath = __dirname;
    this.migrationTable = 'migrations';
  }

  // Create migrations table if it doesn't exist
  async createMigrationsTable() {
    const sql = `
      CREATE TABLE IF NOT EXISTS ${this.migrationTable} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        filename VARCHAR(255) NOT NULL UNIQUE,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `;
    await database.run(sql);
  }

  // Get list of executed migrations
  async getExecutedMigrations() {
    const sql = `SELECT filename FROM ${this.migrationTable} ORDER BY executed_at`;
    const rows = await database.all(sql);
    return rows.map(row => row.filename);
  }

  // Get list of migration files
  getMigrationFiles() {
    const files = fs.readdirSync(this.migrationsPath)
      .filter(file => file.endsWith('.sql'))
      .sort();
    return files;
  }

  // Execute a single migration
  async executeMigration(filename) {
    const filePath = path.join(this.migrationsPath, filename);
    const sql = fs.readFileSync(filePath, 'utf8');
    
    // Split SQL file by semicolons and execute each statement
    const statements = sql.split(';').filter(stmt => stmt.trim().length > 0);
    
    try {
      await database.beginTransaction();
      
      for (const statement of statements) {
        if (statement.trim()) {
          await database.run(statement.trim());
        }
      }
      
      // Record migration as executed
      await database.run(
        `INSERT INTO ${this.migrationTable} (filename) VALUES (?)`,
        [filename]
      );
      
      await database.commit();
      console.log(`✓ Executed migration: ${filename}`);
    } catch (error) {
      await database.rollback();
      throw new Error(`Failed to execute migration ${filename}: ${error.message}`);
    }
  }

  // Run all pending migrations
  async runMigrations() {
    try {
      await database.connect();
      await this.createMigrationsTable();
      
      const executedMigrations = await this.getExecutedMigrations();
      const migrationFiles = this.getMigrationFiles();
      
      const pendingMigrations = migrationFiles.filter(
        file => !executedMigrations.includes(file)
      );
      
      if (pendingMigrations.length === 0) {
        console.log('✓ No pending migrations');
        return;
      }
      
      console.log(`Running ${pendingMigrations.length} pending migration(s)...`);
      
      for (const migration of pendingMigrations) {
        await this.executeMigration(migration);
      }
      
      console.log('✓ All migrations completed successfully');
    } catch (error) {
      console.error('Migration failed:', error.message);
      process.exit(1);
    } finally {
      await database.close();
    }
  }

  // Rollback last migration (basic implementation)
  async rollbackLastMigration() {
    try {
      await database.connect();
      
      const lastMigration = await database.get(
        `SELECT filename FROM ${this.migrationTable} ORDER BY executed_at DESC LIMIT 1`
      );
      
      if (!lastMigration) {
        console.log('No migrations to rollback');
        return;
      }
      
      // For SQLite, we can't easily rollback schema changes
      // This would require specific rollback scripts
      console.log(`Warning: Rollback for ${lastMigration.filename} requires manual intervention`);
      console.log('SQLite doesn\'t support easy schema rollbacks');
      
    } catch (error) {
      console.error('Rollback failed:', error.message);
      process.exit(1);
    } finally {
      await database.close();
    }
  }
}

// Run migrations if this file is executed directly
if (require.main === module) {
  const runner = new MigrationRunner();
  
  const command = process.argv[2];
  
  if (command === 'rollback') {
    runner.rollbackLastMigration();
  } else {
    runner.runMigrations();
  }
}

module.exports = MigrationRunner;
