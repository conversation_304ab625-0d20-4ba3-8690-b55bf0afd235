const express = require('express');
const router = express.Router();
const EmployeeController = require('../controllers/employeeController');
const { authenticate, authorize, authorizeMinRole, auditLog } = require('../middleware/auth');
const {
  validateEmployeeCreate,
  validateEmployeeUpdate,
  validateId,
  validatePagination
} = require('../middleware/validation');

/**
 * @route   GET /api/employees
 * @desc    Get all employees with filtering and pagination
 * @access  Private (HR/Admin can see all, Managers see their team, Employees see limited info)
 */
router.get('/',
  authenticate,
  validatePagination,
  EmployeeController.getAllEmployees
);

/**
 * @route   GET /api/employees/stats
 * @desc    Get employee statistics
 * @access  Private (HR/Admin only)
 */
router.get('/stats',
  authenticate,
  authorizeMinRole('hr'),
  EmployeeController.getEmployeeStats
);

/**
 * @route   POST /api/employees
 * @desc    Create new employee
 * @access  Private (HR/Admin only)
 */
router.post('/',
  authenticate,
  authorizeMinRole('hr'),
  validateEmployeeCreate,
  auditLog('EMPLOYEE_CREATE'),
  EmployeeController.createEmployee
);

/**
 * @route   GET /api/employees/:id
 * @desc    Get employee by ID
 * @access  Private (HR/Admin can see all, Managers see their team, Employees see own profile)
 */
router.get('/:id',
  authenticate,
  validateId,
  EmployeeController.getEmployeeById
);

/**
 * @route   PUT /api/employees/:id
 * @desc    Update employee
 * @access  Private (HR/Admin can update all, Managers can update their team)
 */
router.put('/:id',
  authenticate,
  authorizeMinRole('manager'),
  validateId,
  validateEmployeeUpdate,
  auditLog('EMPLOYEE_UPDATE'),
  EmployeeController.updateEmployee
);

/**
 * @route   DELETE /api/employees/:id
 * @desc    Deactivate employee
 * @access  Private (HR/Admin only)
 */
router.delete('/:id',
  authenticate,
  authorizeMinRole('hr'),
  validateId,
  auditLog('EMPLOYEE_DELETE'),
  EmployeeController.deleteEmployee
);

/**
 * @route   GET /api/employees/:id/subordinates
 * @desc    Get employee's subordinates
 * @access  Private (HR/Admin can see all, Managers see their team)
 */
router.get('/:id/subordinates',
  authenticate,
  authorizeMinRole('manager'),
  validateId,
  EmployeeController.getSubordinates
);

/**
 * @route   GET /api/employees/:id/manager
 * @desc    Get employee's manager
 * @access  Private
 */
router.get('/:id/manager',
  authenticate,
  validateId,
  EmployeeController.getManager
);

/**
 * @route   PUT /api/employees/:id/activate
 * @desc    Activate employee
 * @access  Private (HR/Admin only)
 */
router.put('/:id/activate',
  authenticate,
  authorizeMinRole('hr'),
  validateId,
  auditLog('EMPLOYEE_ACTIVATE'),
  async (req, res) => {
    try {
      const { id } = req.params;
      const Employee = require('../models/Employee');
      
      const employee = await Employee.findById(id);
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: 'Employee not found'
        });
      }

      await employee.reactivate();

      res.json({
        success: true,
        message: 'Employee activated successfully'
      });

    } catch (error) {
      console.error('Activate employee error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }
);

/**
 * @route   PUT /api/employees/:id/deactivate
 * @desc    Deactivate employee
 * @access  Private (HR/Admin only)
 */
router.put('/:id/deactivate',
  authenticate,
  authorizeMinRole('hr'),
  validateId,
  auditLog('EMPLOYEE_DEACTIVATE'),
  async (req, res) => {
    try {
      const { id } = req.params;
      const Employee = require('../models/Employee');
      
      const employee = await Employee.findById(id);
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: 'Employee not found'
        });
      }

      await employee.deactivate();

      res.json({
        success: true,
        message: 'Employee deactivated successfully'
      });

    } catch (error) {
      console.error('Deactivate employee error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }
);

/**
 * @route   PUT /api/employees/:id/terminate
 * @desc    Terminate employee
 * @access  Private (HR/Admin only)
 */
router.put('/:id/terminate',
  authenticate,
  authorizeMinRole('hr'),
  validateId,
  auditLog('EMPLOYEE_TERMINATE'),
  async (req, res) => {
    try {
      const { id } = req.params;
      const Employee = require('../models/Employee');
      
      const employee = await Employee.findById(id);
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: 'Employee not found'
        });
      }

      await employee.terminate();

      res.json({
        success: true,
        message: 'Employee terminated successfully'
      });

    } catch (error) {
      console.error('Terminate employee error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }
);

// Error handling middleware for employee routes
router.use((error, req, res, next) => {
  console.error('Employee route error:', error);
  
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: error.errors
    });
  }
  
  if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
    return res.status(409).json({
      success: false,
      message: 'Employee ID or email already exists'
    });
  }
  
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? error.message : undefined
  });
});

module.exports = router;
