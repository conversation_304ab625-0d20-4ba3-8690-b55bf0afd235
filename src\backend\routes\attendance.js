const express = require('express');
const router = express.Router();
const AttendanceController = require('../controllers/attendanceController');
const { authenticate, authorize, authorizeMinRole, auditLog } = require('../middleware/auth');
const {
  validateAttendance,
  validateId,
  validateDateRange,
  validatePagination
} = require('../middleware/validation');
const { body, query } = require('express-validator');

// Clock in/out validation
const validateClockIn = [
  body('time')
    .optional()
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/)
    .withMessage('Valid time format required (HH:MM:SS)'),
  
  body('location')
    .optional()
    .isLength({ max: 200 })
    .withMessage('Location cannot exceed 200 characters'),
  
  body('notes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Notes cannot exceed 500 characters')
];

const validateClockOut = [
  body('time')
    .optional()
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/)
    .withMessage('Valid time format required (HH:MM:SS)'),
  
  body('location')
    .optional()
    .isLength({ max: 200 })
    .withMessage('Location cannot exceed 200 characters'),
  
  body('notes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Notes cannot exceed 500 characters')
];

const validateBreakTime = [
  body('time')
    .optional()
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/)
    .withMessage('Valid time format required (HH:MM:SS)')
];

const validateMarkAbsent = [
  body('employee_id')
    .notEmpty()
    .withMessage('Employee ID is required')
    .isInt({ min: 1 })
    .withMessage('Valid employee ID required'),
  
  body('date')
    .notEmpty()
    .withMessage('Date is required')
    .isISO8601()
    .withMessage('Valid date format required (YYYY-MM-DD)'),
  
  body('reason')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Reason cannot exceed 500 characters')
];

/**
 * @route   POST /api/attendance/clock-in
 * @desc    Clock in for the day
 * @access  Private (Employee)
 */
router.post('/clock-in',
  authenticate,
  validateClockIn,
  auditLog('CLOCK_IN'),
  AttendanceController.clockIn
);

/**
 * @route   POST /api/attendance/clock-out
 * @desc    Clock out for the day
 * @access  Private (Employee)
 */
router.post('/clock-out',
  authenticate,
  validateClockOut,
  auditLog('CLOCK_OUT'),
  AttendanceController.clockOut
);

/**
 * @route   POST /api/attendance/break-start
 * @desc    Start break
 * @access  Private (Employee)
 */
router.post('/break-start',
  authenticate,
  validateBreakTime,
  auditLog('BREAK_START'),
  AttendanceController.startBreak
);

/**
 * @route   POST /api/attendance/break-end
 * @desc    End break
 * @access  Private (Employee)
 */
router.post('/break-end',
  authenticate,
  validateBreakTime,
  auditLog('BREAK_END'),
  AttendanceController.endBreak
);

/**
 * @route   GET /api/attendance
 * @desc    Get attendance records with filtering
 * @access  Private (Role-based access)
 */
router.get('/',
  authenticate,
  validatePagination,
  validateDateRange,
  AttendanceController.getAttendanceRecords
);

/**
 * @route   GET /api/attendance/today
 * @desc    Get today's attendance status
 * @access  Private (Employee)
 */
router.get('/today',
  authenticate,
  AttendanceController.getTodayStatus
);

/**
 * @route   GET /api/attendance/summary
 * @desc    Get attendance summary for employee
 * @access  Private (Role-based access)
 */
router.get('/summary',
  authenticate,
  validateDateRange,
  query('employee_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Valid employee ID required'),
  AttendanceController.getAttendanceSummary
);

/**
 * @route   GET /api/attendance/stats
 * @desc    Get attendance statistics
 * @access  Private (Manager/HR/Admin)
 */
router.get('/stats',
  authenticate,
  authorizeMinRole('manager'),
  validateDateRange,
  query('department')
    .optional()
    .isLength({ min: 2, max: 50 })
    .withMessage('Department name must be between 2 and 50 characters'),
  AttendanceController.getAttendanceStats
);

/**
 * @route   GET /api/attendance/reports
 * @desc    Generate attendance reports
 * @access  Private (Manager/HR/Admin)
 */
router.get('/reports',
  authenticate,
  authorizeMinRole('manager'),
  validateDateRange,
  query('type')
    .optional()
    .isIn(['summary', 'detailed', 'department'])
    .withMessage('Invalid report type'),
  query('format')
    .optional()
    .isIn(['json', 'csv', 'pdf'])
    .withMessage('Invalid format'),
  AttendanceController.generateReport
);

/**
 * @route   PUT /api/attendance/:id
 * @desc    Update attendance record
 * @access  Private (Manager/HR/Admin or own record)
 */
router.put('/:id',
  authenticate,
  validateId,
  validateAttendance,
  auditLog('ATTENDANCE_UPDATE'),
  AttendanceController.updateAttendance
);

/**
 * @route   POST /api/attendance/mark-absent
 * @desc    Mark employee as absent
 * @access  Private (Manager/HR/Admin)
 */
router.post('/mark-absent',
  authenticate,
  authorizeMinRole('manager'),
  validateMarkAbsent,
  auditLog('MARK_ABSENT'),
  AttendanceController.markAbsent
);

/**
 * @route   GET /api/attendance/late-arrivals
 * @desc    Get late arrivals for a specific date
 * @access  Private (Manager/HR/Admin)
 */
router.get('/late-arrivals',
  authenticate,
  authorizeMinRole('manager'),
  query('date')
    .optional()
    .isISO8601()
    .withMessage('Valid date format required (YYYY-MM-DD)'),
  query('threshold')
    .optional()
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/)
    .withMessage('Valid time format required (HH:MM:SS)'),
  async (req, res) => {
    try {
      const { date = new Date().toISOString().split('T')[0], threshold = '09:00:00' } = req.query;
      const Attendance = require('../models/Attendance');
      
      const lateArrivals = await Attendance.getLateArrivals(date, threshold);
      
      res.json({
        success: true,
        data: { 
          date,
          threshold,
          late_arrivals: lateArrivals 
        }
      });
    } catch (error) {
      console.error('Get late arrivals error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }
);

/**
 * @route   GET /api/attendance/early-departures
 * @desc    Get early departures for a specific date
 * @access  Private (Manager/HR/Admin)
 */
router.get('/early-departures',
  authenticate,
  authorizeMinRole('manager'),
  query('date')
    .optional()
    .isISO8601()
    .withMessage('Valid date format required (YYYY-MM-DD)'),
  query('threshold')
    .optional()
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/)
    .withMessage('Valid time format required (HH:MM:SS)'),
  async (req, res) => {
    try {
      const { date = new Date().toISOString().split('T')[0], threshold = '17:00:00' } = req.query;
      const Attendance = require('../models/Attendance');
      
      const earlyDepartures = await Attendance.getEarlyDepartures(date, threshold);
      
      res.json({
        success: true,
        data: { 
          date,
          threshold,
          early_departures: earlyDepartures 
        }
      });
    } catch (error) {
      console.error('Get early departures error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }
);

/**
 * @route   GET /api/attendance/employee/:employeeId
 * @desc    Get attendance records for specific employee
 * @access  Private (Manager/HR/Admin or own records)
 */
router.get('/employee/:employeeId',
  authenticate,
  validateDateRange,
  validatePagination,
  async (req, res) => {
    try {
      const { employeeId } = req.params;
      const { start_date, end_date, page = 1, limit = 50 } = req.query;

      // Check permissions
      if (req.user.role === 'employee') {
        const Employee = require('../models/Employee');
        const employee = await Employee.findByUserId(req.user.id);
        if (!employee || employee.id !== parseInt(employeeId)) {
          return res.status(403).json({
            success: false,
            message: 'Access denied'
          });
        }
      }

      const filters = {
        employee_id: employeeId,
        start_date,
        end_date,
        limit: parseInt(limit),
        offset: (page - 1) * limit
      };

      const Attendance = require('../models/Attendance');
      const attendanceRecords = await Attendance.findAll(filters);

      res.json({
        success: true,
        data: { attendance: attendanceRecords }
      });

    } catch (error) {
      console.error('Get employee attendance error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }
);

// Error handling middleware for attendance routes
router.use((error, req, res, next) => {
  console.error('Attendance route error:', error);
  
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: error.errors
    });
  }
  
  if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
    return res.status(409).json({
      success: false,
      message: 'Attendance record already exists for this date'
    });
  }
  
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? error.message : undefined
  });
});

module.exports = router;
