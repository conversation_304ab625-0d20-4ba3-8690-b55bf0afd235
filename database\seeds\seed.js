const bcrypt = require('bcrypt');
const database = require('../../src/backend/config/database');

class DatabaseSeeder {
  constructor() {
    this.saltRounds = 12;
  }

  // Hash password
  async hashPassword(password) {
    return await bcrypt.hash(password, this.saltRounds);
  }

  // Clear existing data (for development)
  async clearData() {
    const tables = [
      'audit_logs',
      'sessions', 
      'leave_balances',
      'leave_requests',
      'attendance',
      'employees',
      'users',
      'departments'
    ];

    for (const table of tables) {
      await database.run(`DELETE FROM ${table}`);
      console.log(`✓ Cleared ${table} table`);
    }
  }

  // Seed departments
  async seedDepartments() {
    const departments = [
      { name: 'Human Resources', description: 'HR and employee management' },
      { name: 'Information Technology', description: 'IT support and development' },
      { name: 'Finance', description: 'Financial planning and accounting' },
      { name: 'Marketing', description: 'Marketing and communications' },
      { name: 'Operations', description: 'Daily operations and logistics' },
      { name: 'Sales', description: 'Sales and customer relations' }
    ];

    for (const dept of departments) {
      await database.run(
        'INSERT INTO departments (name, description) VALUES (?, ?)',
        [dept.name, dept.description]
      );
    }
    console.log('✓ Seeded departments');
  }

  // Seed users
  async seedUsers() {
    const users = [
      {
        username: 'admin',
        email: '<EMAIL>',
        password: 'admin123',
        role: 'admin'
      },
      {
        username: 'hr_manager',
        email: '<EMAIL>', 
        password: 'hr123',
        role: 'hr'
      },
      {
        username: 'manager1',
        email: '<EMAIL>',
        password: 'manager123',
        role: 'manager'
      },
      {
        username: 'employee1',
        email: '<EMAIL>',
        password: 'password123',
        role: 'employee'
      },
      {
        username: 'employee2',
        email: '<EMAIL>',
        password: 'password123',
        role: 'employee'
      },
      {
        username: 'employee3',
        email: '<EMAIL>',
        password: 'password123',
        role: 'employee'
      }
    ];

    for (const user of users) {
      const hashedPassword = await this.hashPassword(user.password);
      await database.run(
        'INSERT INTO users (username, email, password_hash, role) VALUES (?, ?, ?, ?)',
        [user.username, user.email, hashedPassword, user.role]
      );
    }
    console.log('✓ Seeded users');
  }

  // Seed employees
  async seedEmployees() {
    const employees = [
      {
        user_id: 1,
        employee_id: 'EMP001',
        first_name: 'System',
        last_name: 'Administrator',
        email: '<EMAIL>',
        phone: '******-0001',
        department: 'Information Technology',
        position: 'System Administrator',
        hire_date: '2023-01-01',
        salary: 80000.00,
        status: 'active'
      },
      {
        user_id: 2,
        employee_id: 'EMP002',
        first_name: 'Sarah',
        last_name: 'Johnson',
        email: '<EMAIL>',
        phone: '******-0002',
        department: 'Human Resources',
        position: 'HR Manager',
        hire_date: '2023-01-15',
        salary: 75000.00,
        status: 'active'
      },
      {
        user_id: 3,
        employee_id: 'EMP003',
        first_name: 'Michael',
        last_name: 'Smith',
        email: '<EMAIL>',
        phone: '******-0003',
        department: 'Sales',
        position: 'Sales Manager',
        hire_date: '2023-02-01',
        salary: 70000.00,
        status: 'active'
      },
      {
        user_id: 4,
        employee_id: 'EMP004',
        first_name: 'Emily',
        last_name: 'Davis',
        email: '<EMAIL>',
        phone: '******-0004',
        department: 'Marketing',
        position: 'Marketing Specialist',
        hire_date: '2023-03-01',
        salary: 55000.00,
        manager_id: 3,
        status: 'active'
      },
      {
        user_id: 5,
        employee_id: 'EMP005',
        first_name: 'David',
        last_name: 'Wilson',
        email: '<EMAIL>',
        phone: '******-0005',
        department: 'Information Technology',
        position: 'Software Developer',
        hire_date: '2023-03-15',
        salary: 65000.00,
        manager_id: 1,
        status: 'active'
      },
      {
        user_id: 6,
        employee_id: 'EMP006',
        first_name: 'Lisa',
        last_name: 'Brown',
        email: '<EMAIL>',
        phone: '******-0006',
        department: 'Finance',
        position: 'Accountant',
        hire_date: '2023-04-01',
        salary: 50000.00,
        status: 'active'
      }
    ];

    for (const emp of employees) {
      await database.run(`
        INSERT INTO employees (
          user_id, employee_id, first_name, last_name, email, phone,
          department, position, hire_date, salary, manager_id, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        emp.user_id, emp.employee_id, emp.first_name, emp.last_name,
        emp.email, emp.phone, emp.department, emp.position,
        emp.hire_date, emp.salary, emp.manager_id || null, emp.status
      ]);
    }
    console.log('✓ Seeded employees');
  }

  // Seed leave balances for current year
  async seedLeaveBalances() {
    const currentYear = new Date().getFullYear();
    const leaveTypes = ['vacation', 'sick', 'personal'];
    const defaultBalances = { vacation: 20, sick: 10, personal: 5 };

    // Get all employees
    const employees = await database.all('SELECT id FROM employees');

    for (const employee of employees) {
      for (const leaveType of leaveTypes) {
        await database.run(`
          INSERT INTO leave_balances (employee_id, year, leave_type, total_days, remaining_days)
          VALUES (?, ?, ?, ?, ?)
        `, [
          employee.id,
          currentYear,
          leaveType,
          defaultBalances[leaveType],
          defaultBalances[leaveType]
        ]);
      }
    }
    console.log('✓ Seeded leave balances');
  }

  // Seed sample attendance data for last 30 days
  async seedSampleAttendance() {
    const employees = await database.all('SELECT id FROM employees WHERE id > 1'); // Skip admin
    const today = new Date();
    
    for (let i = 30; i >= 1; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      
      // Skip weekends
      if (date.getDay() === 0 || date.getDay() === 6) continue;
      
      const dateStr = date.toISOString().split('T')[0];
      
      for (const employee of employees) {
        // 90% attendance rate
        if (Math.random() > 0.1) {
          const clockIn = '09:00:00';
          const clockOut = '17:30:00';
          const totalHours = 8.5;
          
          await database.run(`
            INSERT INTO attendance (employee_id, date, clock_in, clock_out, total_hours, status)
            VALUES (?, ?, ?, ?, ?, ?)
          `, [employee.id, dateStr, clockIn, clockOut, totalHours, 'present']);
        }
      }
    }
    console.log('✓ Seeded sample attendance data');
  }

  // Run all seeds
  async runSeeds() {
    try {
      await database.connect();
      
      console.log('Starting database seeding...');
      
      // Clear existing data in development
      if (process.env.NODE_ENV !== 'production') {
        await this.clearData();
      }
      
      await this.seedDepartments();
      await this.seedUsers();
      await this.seedEmployees();
      await this.seedLeaveBalances();
      await this.seedSampleAttendance();
      
      console.log('✓ Database seeding completed successfully');
      console.log('\nDefault login credentials:');
      console.log('Admin: admin / admin123');
      console.log('HR Manager: hr_manager / hr123');
      console.log('Manager: manager1 / manager123');
      console.log('Employee: employee1 / password123');
      
    } catch (error) {
      console.error('Seeding failed:', error.message);
      process.exit(1);
    } finally {
      await database.close();
    }
  }
}

// Run seeds if this file is executed directly
if (require.main === module) {
  const seeder = new DatabaseSeeder();
  seeder.runSeeds();
}

module.exports = DatabaseSeeder;
