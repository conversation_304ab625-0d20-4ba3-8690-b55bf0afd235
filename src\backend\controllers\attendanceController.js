const Attendance = require('../models/Attendance');
const Employee = require('../models/Employee');
const database = require('../config/database');
const Helpers = require('../utils/helpers');
const { validationResult } = require('express-validator');

class AttendanceController {
  // Clock in
  static async clockIn(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      // Get employee from authenticated user
      const employee = await Employee.findByUserId(req.user.id);
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: 'Employee record not found'
        });
      }

      const clockInData = {
        time: req.body.time,
        location: req.body.location,
        ip_address: req.ip,
        notes: req.body.notes
      };

      const attendance = await Attendance.clockIn(employee.id, clockInData);

      // Log audit
      await database.run(`
        INSERT INTO audit_logs (user_id, action, table_name, record_id, new_values, ip_address, user_agent)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        req.user.id,
        'CLOCK_IN',
        'attendance',
        attendance.id,
        JSON.stringify(clockInData),
        req.ip,
        req.get('User-Agent')
      ]);

      res.json({
        success: true,
        message: 'Clocked in successfully',
        data: { attendance }
      });

    } catch (error) {
      console.error('Clock in error:', error);
      res.status(400).json({
        success: false,
        message: error.message || 'Clock in failed',
        error: error.message
      });
    }
  }

  // Clock out
  static async clockOut(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      // Get employee from authenticated user
      const employee = await Employee.findByUserId(req.user.id);
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: 'Employee record not found'
        });
      }

      const today = Helpers.formatDate(new Date());
      const attendance = await Attendance.findByEmployeeAndDate(employee.id, today);

      if (!attendance) {
        return res.status(400).json({
          success: false,
          message: 'No clock in record found for today'
        });
      }

      const clockOutData = {
        time: req.body.time,
        location: req.body.location,
        notes: req.body.notes
      };

      await attendance.clockOut(clockOutData);

      // Log audit
      await database.run(`
        INSERT INTO audit_logs (user_id, action, table_name, record_id, new_values, ip_address, user_agent)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        req.user.id,
        'CLOCK_OUT',
        'attendance',
        attendance.id,
        JSON.stringify(clockOutData),
        req.ip,
        req.get('User-Agent')
      ]);

      res.json({
        success: true,
        message: 'Clocked out successfully',
        data: { attendance }
      });

    } catch (error) {
      console.error('Clock out error:', error);
      res.status(400).json({
        success: false,
        message: error.message || 'Clock out failed',
        error: error.message
      });
    }
  }

  // Start break
  static async startBreak(req, res) {
    try {
      // Get employee from authenticated user
      const employee = await Employee.findByUserId(req.user.id);
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: 'Employee record not found'
        });
      }

      const today = Helpers.formatDate(new Date());
      const attendance = await Attendance.findByEmployeeAndDate(employee.id, today);

      if (!attendance) {
        return res.status(400).json({
          success: false,
          message: 'No attendance record found for today'
        });
      }

      const breakData = {
        time: req.body.time
      };

      await attendance.startBreak(breakData);

      res.json({
        success: true,
        message: 'Break started successfully',
        data: { attendance }
      });

    } catch (error) {
      console.error('Start break error:', error);
      res.status(400).json({
        success: false,
        message: error.message || 'Start break failed',
        error: error.message
      });
    }
  }

  // End break
  static async endBreak(req, res) {
    try {
      // Get employee from authenticated user
      const employee = await Employee.findByUserId(req.user.id);
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: 'Employee record not found'
        });
      }

      const today = Helpers.formatDate(new Date());
      const attendance = await Attendance.findByEmployeeAndDate(employee.id, today);

      if (!attendance) {
        return res.status(400).json({
          success: false,
          message: 'No attendance record found for today'
        });
      }

      const breakData = {
        time: req.body.time
      };

      await attendance.endBreak(breakData);

      res.json({
        success: true,
        message: 'Break ended successfully',
        data: { attendance }
      });

    } catch (error) {
      console.error('End break error:', error);
      res.status(400).json({
        success: false,
        message: error.message || 'End break failed',
        error: error.message
      });
    }
  }

  // Get attendance records
  static async getAttendanceRecords(req, res) {
    try {
      const {
        employee_id,
        department,
        start_date,
        end_date,
        status,
        page = 1,
        limit = 50
      } = req.query;

      const filters = {};

      // Role-based filtering
      if (req.user.role === 'employee') {
        const employee = await Employee.findByUserId(req.user.id);
        if (employee) {
          filters.employee_id = employee.id;
        }
      } else if (req.user.role === 'manager') {
        const managerEmployee = await Employee.findByUserId(req.user.id);
        if (managerEmployee) {
          filters.manager_id = managerEmployee.id;
        }
      }

      // Apply query filters
      if (employee_id && ['hr', 'admin'].includes(req.user.role)) {
        filters.employee_id = employee_id;
      }
      if (department) filters.department = department;
      if (start_date) filters.start_date = start_date;
      if (end_date) filters.end_date = end_date;
      if (status) filters.status = status;

      // Pagination
      const offset = (page - 1) * limit;
      filters.limit = parseInt(limit);
      filters.offset = parseInt(offset);

      const attendanceRecords = await Attendance.findAll(filters);

      // Get total count for pagination
      const countFilters = { ...filters };
      delete countFilters.limit;
      delete countFilters.offset;
      const totalRecords = await Attendance.findAll(countFilters);

      res.json({
        success: true,
        data: {
          attendance: attendanceRecords,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: totalRecords.length,
            pages: Math.ceil(totalRecords.length / limit)
          }
        }
      });

    } catch (error) {
      console.error('Get attendance records error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Get today's attendance status
  static async getTodayStatus(req, res) {
    try {
      // Get employee from authenticated user
      const employee = await Employee.findByUserId(req.user.id);
      if (!employee) {
        return res.status(404).json({
          success: false,
          message: 'Employee record not found'
        });
      }

      const today = Helpers.formatDate(new Date());
      const attendance = await Attendance.findByEmployeeAndDate(employee.id, today);

      const status = {
        date: today,
        has_clocked_in: attendance ? !!attendance.clock_in : false,
        has_clocked_out: attendance ? !!attendance.clock_out : false,
        is_on_break: attendance ? attendance.isOnBreak() : false,
        clock_in_time: attendance ? attendance.clock_in : null,
        clock_out_time: attendance ? attendance.clock_out : null,
        break_start_time: attendance ? attendance.break_start : null,
        break_end_time: attendance ? attendance.break_end : null,
        total_hours: attendance ? attendance.total_hours : 0,
        status: attendance ? attendance.status : 'absent'
      };

      res.json({
        success: true,
        data: { status }
      });

    } catch (error) {
      console.error('Get today status error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Update attendance record
  static async updateAttendance(req, res) {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
      }

      const { id } = req.params;
      const updateData = req.body;

      const attendance = await Attendance.findById(id);
      if (!attendance) {
        return res.status(404).json({
          success: false,
          message: 'Attendance record not found'
        });
      }

      // Check permissions
      if (req.user.role === 'employee') {
        const employee = await Employee.findByUserId(req.user.id);
        if (!employee || attendance.employee_id !== employee.id) {
          return res.status(403).json({
            success: false,
            message: 'Access denied'
          });
        }
      }

      // Store old values for audit
      const oldValues = { ...attendance };

      // Update attendance
      await attendance.update(updateData);

      // Log audit
      await database.run(`
        INSERT INTO audit_logs (user_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        req.user.id,
        'ATTENDANCE_UPDATE',
        'attendance',
        attendance.id,
        JSON.stringify(oldValues),
        JSON.stringify(updateData),
        req.ip,
        req.get('User-Agent')
      ]);

      res.json({
        success: true,
        message: 'Attendance updated successfully',
        data: { attendance }
      });

    } catch (error) {
      console.error('Update attendance error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Mark employee as absent
  static async markAbsent(req, res) {
    try {
      const { employee_id, date, reason } = req.body;

      // Validate permissions
      if (!['hr', 'admin', 'manager'].includes(req.user.role)) {
        return res.status(403).json({
          success: false,
          message: 'Insufficient permissions'
        });
      }

      const attendance = await Attendance.markAbsent(employee_id, date, reason);

      // Log audit
      await database.run(`
        INSERT INTO audit_logs (user_id, action, table_name, record_id, new_values, ip_address, user_agent)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        req.user.id,
        'MARK_ABSENT',
        'attendance',
        attendance.id,
        JSON.stringify({ employee_id, date, reason }),
        req.ip,
        req.get('User-Agent')
      ]);

      res.json({
        success: true,
        message: 'Employee marked as absent',
        data: { attendance }
      });

    } catch (error) {
      console.error('Mark absent error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Get attendance summary
  static async getAttendanceSummary(req, res) {
    try {
      const { employee_id, start_date, end_date } = req.query;

      // Default to current month if no dates provided
      const startDate = start_date || new Date(new Date().getFullYear(), new Date().getMonth(), 1).toISOString().split('T')[0];
      const endDate = end_date || new Date().toISOString().split('T')[0];

      let targetEmployeeId = employee_id;

      // Role-based access control
      if (req.user.role === 'employee') {
        const employee = await Employee.findByUserId(req.user.id);
        if (employee) {
          targetEmployeeId = employee.id;
        }
      }

      if (!targetEmployeeId) {
        return res.status(400).json({
          success: false,
          message: 'Employee ID is required'
        });
      }

      const summary = await Attendance.getEmployeeSummary(targetEmployeeId, startDate, endDate);

      res.json({
        success: true,
        data: { summary }
      });

    } catch (error) {
      console.error('Get attendance summary error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Get attendance statistics
  static async getAttendanceStats(req, res) {
    try {
      const { department, start_date, end_date } = req.query;

      const filters = {};
      if (department) filters.department = department;
      if (start_date) filters.start_date = start_date;
      if (end_date) filters.end_date = end_date;

      const stats = await Attendance.getStatistics(filters);

      // Get late arrivals and early departures for today
      const today = Helpers.formatDate(new Date());
      const lateArrivals = await Attendance.getLateArrivals(today);
      const earlyDepartures = await Attendance.getEarlyDepartures(today);

      res.json({
        success: true,
        data: {
          statistics: stats,
          today: {
            late_arrivals: lateArrivals,
            early_departures: earlyDepartures
          }
        }
      });

    } catch (error) {
      console.error('Get attendance stats error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }

  // Generate attendance report
  static async generateReport(req, res) {
    try {
      const { 
        type = 'summary', 
        start_date, 
        end_date, 
        department, 
        employee_id,
        format = 'json'
      } = req.query;

      const filters = {};
      if (start_date) filters.start_date = start_date;
      if (end_date) filters.end_date = end_date;
      if (department) filters.department = department;
      if (employee_id) filters.employee_id = employee_id;

      let reportData = {};

      switch (type) {
        case 'summary':
          reportData = await Attendance.getStatistics(filters);
          break;
        
        case 'detailed':
          reportData = await Attendance.findAll(filters);
          break;
        
        case 'department':
          if (department) {
            reportData = await Attendance.getDepartmentSummary(department, filters.start_date, filters.end_date);
          }
          break;
        
        default:
          return res.status(400).json({
            success: false,
            message: 'Invalid report type'
          });
      }

      res.json({
        success: true,
        data: {
          report_type: type,
          filters,
          generated_at: new Date().toISOString(),
          data: reportData
        }
      });

    } catch (error) {
      console.error('Generate report error:', error);
      res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: error.message
      });
    }
  }
}

module.exports = AttendanceController;
