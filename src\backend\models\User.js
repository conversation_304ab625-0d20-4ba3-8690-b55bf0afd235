const database = require('../config/database');
const bcrypt = require('bcrypt');

class User {
  constructor(data = {}) {
    this.id = data.id;
    this.username = data.username;
    this.email = data.email;
    this.password_hash = data.password_hash;
    this.role = data.role;
    this.is_active = data.is_active;
    this.last_login = data.last_login;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  // Create new user
  static async create(userData) {
    const { username, email, password, role = 'employee' } = userData;
    
    // Hash password
    const saltRounds = 12;
    const password_hash = await bcrypt.hash(password, saltRounds);
    
    const sql = `
      INSERT INTO users (username, email, password_hash, role)
      VALUES (?, ?, ?, ?)
    `;
    
    const result = await database.run(sql, [username, email, password_hash, role]);
    
    // Return the created user
    return await User.findById(result.id);
  }

  // Find user by ID
  static async findById(id) {
    const sql = 'SELECT * FROM users WHERE id = ?';
    const row = await database.get(sql, [id]);
    return row ? new User(row) : null;
  }

  // Find user by username
  static async findByUsername(username) {
    const sql = 'SELECT * FROM users WHERE username = ?';
    const row = await database.get(sql, [username]);
    return row ? new User(row) : null;
  }

  // Find user by email
  static async findByEmail(email) {
    const sql = 'SELECT * FROM users WHERE email = ?';
    const row = await database.get(sql, [email]);
    return row ? new User(row) : null;
  }

  // Get all users
  static async findAll(filters = {}) {
    let sql = 'SELECT * FROM users WHERE 1=1';
    const params = [];

    if (filters.role) {
      sql += ' AND role = ?';
      params.push(filters.role);
    }

    if (filters.is_active !== undefined) {
      sql += ' AND is_active = ?';
      params.push(filters.is_active);
    }

    sql += ' ORDER BY created_at DESC';

    const rows = await database.all(sql, params);
    return rows.map(row => new User(row));
  }

  // Verify password
  async verifyPassword(password) {
    return await bcrypt.compare(password, this.password_hash);
  }

  // Update user
  async update(updateData) {
    const allowedFields = ['username', 'email', 'role', 'is_active'];
    const updates = [];
    const params = [];

    for (const [key, value] of Object.entries(updateData)) {
      if (allowedFields.includes(key)) {
        updates.push(`${key} = ?`);
        params.push(value);
      }
    }

    if (updates.length === 0) {
      throw new Error('No valid fields to update');
    }

    updates.push('updated_at = CURRENT_TIMESTAMP');
    params.push(this.id);

    const sql = `UPDATE users SET ${updates.join(', ')} WHERE id = ?`;
    await database.run(sql, params);

    // Refresh the instance
    const updated = await User.findById(this.id);
    Object.assign(this, updated);
    return this;
  }

  // Update password
  async updatePassword(newPassword) {
    const saltRounds = 12;
    const password_hash = await bcrypt.hash(newPassword, saltRounds);
    
    const sql = 'UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?';
    await database.run(sql, [password_hash, this.id]);
    
    this.password_hash = password_hash;
    return this;
  }

  // Update last login
  async updateLastLogin() {
    const sql = 'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?';
    await database.run(sql, [this.id]);
    this.last_login = new Date().toISOString();
    return this;
  }

  // Deactivate user
  async deactivate() {
    return await this.update({ is_active: false });
  }

  // Activate user
  async activate() {
    return await this.update({ is_active: true });
  }

  // Delete user (soft delete by deactivating)
  async delete() {
    return await this.deactivate();
  }

  // Hard delete user (use with caution)
  async hardDelete() {
    const sql = 'DELETE FROM users WHERE id = ?';
    await database.run(sql, [this.id]);
    return true;
  }

  // Get user with employee details
  async getWithEmployee() {
    const sql = `
      SELECT u.*, e.employee_id, e.first_name, e.last_name, e.department, e.position
      FROM users u
      LEFT JOIN employees e ON u.id = e.user_id
      WHERE u.id = ?
    `;
    const row = await database.get(sql, [this.id]);
    return row;
  }

  // Check if user has permission
  hasPermission(requiredRole) {
    const roleHierarchy = {
      'admin': 4,
      'hr': 3,
      'manager': 2,
      'employee': 1
    };

    const userLevel = roleHierarchy[this.role] || 0;
    const requiredLevel = roleHierarchy[requiredRole] || 0;

    return userLevel >= requiredLevel;
  }

  // Convert to JSON (exclude sensitive data)
  toJSON() {
    const { password_hash, ...userWithoutPassword } = this;
    return userWithoutPassword;
  }

  // Validate user data
  static validate(userData) {
    const errors = [];

    if (!userData.username || userData.username.length < 3) {
      errors.push('Username must be at least 3 characters long');
    }

    if (!userData.email || !/\S+@\S+\.\S+/.test(userData.email)) {
      errors.push('Valid email is required');
    }

    if (!userData.password || userData.password.length < 6) {
      errors.push('Password must be at least 6 characters long');
    }

    const validRoles = ['admin', 'hr', 'manager', 'employee'];
    if (userData.role && !validRoles.includes(userData.role)) {
      errors.push('Invalid role specified');
    }

    return errors;
  }
}

module.exports = User;
