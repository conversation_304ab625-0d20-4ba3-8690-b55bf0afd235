const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const path = require('path');
require('dotenv').config();

const database = require('./config/database');
const authRoutes = require('./routes/auth');
const employeeRoutes = require('./routes/employees');
const departmentRoutes = require('./routes/departments');
const attendanceRoutes = require('./routes/attendance');

class Server {
  constructor() {
    this.app = express();
    this.port = process.env.PORT || 3000;
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  setupMiddleware() {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'", "https://cdnjs.cloudflare.com", "https://cdn.jsdelivr.net"],
          scriptSrc: ["'self'", "https://cdnjs.cloudflare.com", "https://cdn.jsdelivr.net"],
          imgSrc: ["'self'", "data:", "https:"],
          fontSrc: ["'self'", "https://cdnjs.cloudflare.com", "https://cdn.jsdelivr.net"]
        }
      }
    }));

    // CORS configuration
    this.app.use(cors({
      origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
      credentials: process.env.CORS_CREDENTIALS === 'true',
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization']
    }));

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Static files middleware
    this.app.use(express.static(path.join(__dirname, '../frontend')));

    // Request logging middleware
    this.app.use((req, res, next) => {
      console.log(`${new Date().toISOString()} - ${req.method} ${req.path} - ${req.ip}`);
      next();
    });

    // Add request timestamp
    this.app.use((req, res, next) => {
      req.timestamp = new Date().toISOString();
      next();
    });
  }

  setupRoutes() {
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({
        success: true,
        message: 'HRM System API is running',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      });
    });

    // API routes
    this.app.use('/api/auth', authRoutes);
    this.app.use('/api/employees', employeeRoutes);
    this.app.use('/api/departments', departmentRoutes);
    this.app.use('/api/attendance', attendanceRoutes);

    // Serve frontend pages
    this.app.get('/', (req, res) => {
      res.sendFile(path.join(__dirname, '../frontend/pages/index.html'));
    });

    this.app.get('/login', (req, res) => {
      res.sendFile(path.join(__dirname, '../frontend/pages/login.html'));
    });

    this.app.get('/signup', (req, res) => {
      res.sendFile(path.join(__dirname, '../frontend/pages/signup.html'));
    });

    this.app.get('/dashboard', (req, res) => {
      res.sendFile(path.join(__dirname, '../frontend/pages/dashboard.html'));
    });

    // API documentation endpoint
    this.app.get('/api', (req, res) => {
      res.json({
        success: true,
        message: 'HRM System API',
        version: '1.0.0',
        endpoints: {
          auth: {
            'POST /api/auth/signup': 'User registration',
            'POST /api/auth/login': 'User login',
            'POST /api/auth/logout': 'User logout',
            'POST /api/auth/refresh': 'Refresh access token',
            'GET /api/auth/profile': 'Get user profile',
            'PUT /api/auth/profile': 'Update user profile',
            'PUT /api/auth/change-password': 'Change password',
            'GET /api/auth/sessions': 'Get user sessions',
            'DELETE /api/auth/sessions/:id': 'Revoke session',
            'GET /api/auth/verify': 'Verify token',
            'GET /api/auth/me': 'Get current user'
          },
          employees: {
            'GET /api/employees': 'Get all employees',
            'POST /api/employees': 'Create employee',
            'GET /api/employees/:id': 'Get employee details',
            'PUT /api/employees/:id': 'Update employee',
            'DELETE /api/employees/:id': 'Deactivate employee',
            'GET /api/employees/:id/subordinates': 'Get subordinates',
            'GET /api/employees/:id/manager': 'Get manager',
            'GET /api/employees/stats': 'Get employee statistics'
          },
          departments: {
            'GET /api/departments': 'Get all departments',
            'POST /api/departments': 'Create department',
            'GET /api/departments/:id': 'Get department details',
            'PUT /api/departments/:id': 'Update department',
            'DELETE /api/departments/:id': 'Delete department',
            'GET /api/departments/:id/employees': 'Get department employees',
            'GET /api/departments/:id/stats': 'Get department statistics'
          },
          attendance: {
            'POST /api/attendance/clock-in': 'Clock in',
            'POST /api/attendance/clock-out': 'Clock out',
            'POST /api/attendance/break-start': 'Start break',
            'POST /api/attendance/break-end': 'End break',
            'GET /api/attendance': 'Get attendance records',
            'GET /api/attendance/today': 'Get today status',
            'GET /api/attendance/summary': 'Get attendance summary',
            'GET /api/attendance/stats': 'Get attendance statistics',
            'GET /api/attendance/reports': 'Generate reports',
            'PUT /api/attendance/:id': 'Update attendance',
            'POST /api/attendance/mark-absent': 'Mark absent'
          }
        }
      });
    });

    // 404 handler for API routes
    this.app.use('/api/*', (req, res) => {
      res.status(404).json({
        success: false,
        message: 'API endpoint not found',
        path: req.path
      });
    });

    // Catch-all handler for frontend routes
    this.app.get('*', (req, res) => {
      res.sendFile(path.join(__dirname, '../frontend/pages/index.html'));
    });
  }

  setupErrorHandling() {
    // Global error handler
    this.app.use((error, req, res, next) => {
      console.error('Global error handler:', error);

      // Database errors
      if (error.code === 'SQLITE_CONSTRAINT') {
        return res.status(400).json({
          success: false,
          message: 'Database constraint violation',
          error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
      }

      // Validation errors
      if (error.name === 'ValidationError') {
        return res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: error.errors
        });
      }

      // JWT errors
      if (error.name === 'JsonWebTokenError') {
        return res.status(401).json({
          success: false,
          message: 'Invalid token'
        });
      }

      if (error.name === 'TokenExpiredError') {
        return res.status(401).json({
          success: false,
          message: 'Token expired'
        });
      }

      // Default error response
      res.status(error.status || 500).json({
        success: false,
        message: error.message || 'Internal server error',
        error: process.env.NODE_ENV === 'development' ? error.stack : undefined
      });
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      console.error('Uncaught Exception:', error);
      process.exit(1);
    });
  }

  async start() {
    try {
      // Initialize database connection
      await database.connect();
      console.log('✓ Database connected successfully');

      // Start server
      this.app.listen(this.port, () => {
        console.log(`✓ HRM System server running on port ${this.port}`);
        console.log(`✓ Environment: ${process.env.NODE_ENV || 'development'}`);
        console.log(`✓ API Documentation: http://localhost:${this.port}/api`);
        console.log(`✓ Health Check: http://localhost:${this.port}/health`);
        console.log(`✓ Frontend: http://localhost:${this.port}`);
      });

    } catch (error) {
      console.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  async stop() {
    try {
      await database.close();
      console.log('✓ Server stopped gracefully');
    } catch (error) {
      console.error('Error stopping server:', error);
    }
  }
}

// Create and start server
const server = new Server();

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully');
  await server.stop();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully');
  await server.stop();
  process.exit(0);
});

// Start the server
if (require.main === module) {
  server.start();
}

module.exports = server;
