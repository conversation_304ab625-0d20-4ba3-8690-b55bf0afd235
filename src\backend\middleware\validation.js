const { body, param, query } = require('express-validator');

// User registration validation
const validateSignup = [
  body('username')
    .isLength({ min: 3, max: 50 })
    .withMessage('Username must be between 3 and 50 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores'),
  
  body('email')
    .isEmail()
    .withMessage('Valid email is required')
    .normalizeEmail(),
  
  body('password')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  
  body('role')
    .optional()
    .isIn(['admin', 'hr', 'manager', 'employee'])
    .withMessage('Invalid role specified')
];

// User login validation
const validateLogin = [
  body('username')
    .notEmpty()
    .withMessage('Username is required')
    .trim(),
  
  body('password')
    .notEmpty()
    .withMessage('Password is required')
];

// Profile update validation
const validateProfileUpdate = [
  body('email')
    .optional()
    .isEmail()
    .withMessage('Valid email is required')
    .normalizeEmail()
];

// Password change validation
const validatePasswordChange = [
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),
  
  body('newPassword')
    .isLength({ min: 6 })
    .withMessage('New password must be at least 6 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('New password must contain at least one lowercase letter, one uppercase letter, and one number'),
  
  body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body.newPassword) {
        throw new Error('Password confirmation does not match');
      }
      return true;
    })
];

// Employee creation validation
const validateEmployeeCreate = [
  body('employee_id')
    .notEmpty()
    .withMessage('Employee ID is required')
    .isLength({ min: 3, max: 20 })
    .withMessage('Employee ID must be between 3 and 20 characters')
    .matches(/^[A-Z0-9]+$/)
    .withMessage('Employee ID can only contain uppercase letters and numbers'),
  
  body('first_name')
    .notEmpty()
    .withMessage('First name is required')
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('First name can only contain letters and spaces'),
  
  body('last_name')
    .notEmpty()
    .withMessage('Last name is required')
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('Last name can only contain letters and spaces'),
  
  body('email')
    .optional()
    .isEmail()
    .withMessage('Valid email is required')
    .normalizeEmail(),
  
  body('phone')
    .optional()
    .matches(/^[\+]?[1-9][\d]{0,15}$/)
    .withMessage('Valid phone number is required'),
  
  body('department')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Department name cannot exceed 50 characters'),
  
  body('position')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Position cannot exceed 50 characters'),
  
  body('hire_date')
    .optional()
    .isISO8601()
    .withMessage('Valid hire date is required (YYYY-MM-DD format)'),
  
  body('salary')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Salary must be a positive number'),
  
  body('manager_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Manager ID must be a positive integer'),
  
  body('status')
    .optional()
    .isIn(['active', 'inactive', 'terminated'])
    .withMessage('Invalid status specified')
];

// Employee update validation
const validateEmployeeUpdate = [
  body('first_name')
    .optional()
    .isLength({ min: 2, max: 50 })
    .withMessage('First name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('First name can only contain letters and spaces'),
  
  body('last_name')
    .optional()
    .isLength({ min: 2, max: 50 })
    .withMessage('Last name must be between 2 and 50 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('Last name can only contain letters and spaces'),
  
  body('email')
    .optional()
    .isEmail()
    .withMessage('Valid email is required')
    .normalizeEmail(),
  
  body('phone')
    .optional()
    .matches(/^[\+]?[1-9][\d]{0,15}$/)
    .withMessage('Valid phone number is required'),
  
  body('department')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Department name cannot exceed 50 characters'),
  
  body('position')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Position cannot exceed 50 characters'),
  
  body('salary')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Salary must be a positive number'),
  
  body('manager_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Manager ID must be a positive integer'),
  
  body('status')
    .optional()
    .isIn(['active', 'inactive', 'terminated'])
    .withMessage('Invalid status specified')
];

// Attendance validation
const validateAttendance = [
  body('employee_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Employee ID must be a positive integer'),
  
  body('date')
    .optional()
    .isISO8601()
    .withMessage('Valid date is required (YYYY-MM-DD format)'),
  
  body('clock_in')
    .optional()
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/)
    .withMessage('Valid clock in time is required (HH:MM:SS format)'),
  
  body('clock_out')
    .optional()
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/)
    .withMessage('Valid clock out time is required (HH:MM:SS format)'),
  
  body('status')
    .optional()
    .isIn(['present', 'absent', 'late', 'half_day', 'holiday'])
    .withMessage('Invalid attendance status'),
  
  body('notes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Notes cannot exceed 500 characters')
];

// Leave request validation
const validateLeaveRequest = [
  body('leave_type')
    .notEmpty()
    .withMessage('Leave type is required')
    .isIn(['sick', 'vacation', 'personal', 'emergency', 'maternity', 'paternity'])
    .withMessage('Invalid leave type'),
  
  body('start_date')
    .notEmpty()
    .withMessage('Start date is required')
    .isISO8601()
    .withMessage('Valid start date is required (YYYY-MM-DD format)'),
  
  body('end_date')
    .notEmpty()
    .withMessage('End date is required')
    .isISO8601()
    .withMessage('Valid end date is required (YYYY-MM-DD format)')
    .custom((value, { req }) => {
      if (new Date(value) < new Date(req.body.start_date)) {
        throw new Error('End date must be after start date');
      }
      return true;
    }),
  
  body('reason')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Reason cannot exceed 500 characters')
];

// Leave approval validation
const validateLeaveApproval = [
  body('status')
    .notEmpty()
    .withMessage('Status is required')
    .isIn(['approved', 'rejected'])
    .withMessage('Status must be either approved or rejected'),
  
  body('rejection_reason')
    .if(body('status').equals('rejected'))
    .notEmpty()
    .withMessage('Rejection reason is required when rejecting leave')
    .isLength({ max: 500 })
    .withMessage('Rejection reason cannot exceed 500 characters')
];

// ID parameter validation
const validateId = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Valid ID is required')
];

// Date range query validation
const validateDateRange = [
  query('start_date')
    .optional()
    .isISO8601()
    .withMessage('Valid start date is required (YYYY-MM-DD format)'),
  
  query('end_date')
    .optional()
    .isISO8601()
    .withMessage('Valid end date is required (YYYY-MM-DD format)')
];

// Pagination validation
const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Page must be a positive integer'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100')
];

module.exports = {
  validateSignup,
  validateLogin,
  validateProfileUpdate,
  validatePasswordChange,
  validateEmployeeCreate,
  validateEmployeeUpdate,
  validateAttendance,
  validateLeaveRequest,
  validateLeaveApproval,
  validateId,
  validateDateRange,
  validatePagination
};
