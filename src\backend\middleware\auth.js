const jwtUtils = require('../utils/jwt');
const User = require('../models/User');
const database = require('../config/database');

// Authentication middleware
const authenticate = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return res.status(401).json({
        success: false,
        message: 'Access token required'
      });
    }

    const token = jwtUtils.extractTokenFromHeader(authHeader);
    const decoded = jwtUtils.verifyAccessToken(token);

    // Check if token exists in sessions table (for logout functionality)
    const tokenHash = jwtUtils.hashToken(token);
    const session = await database.get(
      'SELECT * FROM sessions WHERE token_hash = ? AND expires_at > CURRENT_TIMESTAMP',
      [tokenHash]
    );

    if (!session) {
      return res.status(401).json({
        success: false,
        message: 'Invalid or expired session'
      });
    }

    // Get user details
    const user = await User.findById(decoded.id);
    if (!user || !user.is_active) {
      return res.status(401).json({
        success: false,
        message: 'User not found or inactive'
      });
    }

    // Update session last used
    await database.run(
      'UPDATE sessions SET last_used = CURRENT_TIMESTAMP WHERE id = ?',
      [session.id]
    );

    // Attach user to request
    req.user = user;
    req.session = session;
    next();

  } catch (error) {
    return res.status(401).json({
      success: false,
      message: 'Invalid token',
      error: error.message
    });
  }
};

// Authorization middleware - check user role
const authorize = (...roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    if (!roles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions'
      });
    }

    next();
  };
};

// Role hierarchy authorization
const authorizeMinRole = (minRole) => {
  const roleHierarchy = {
    'employee': 1,
    'manager': 2,
    'hr': 3,
    'admin': 4
  };

  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    const userLevel = roleHierarchy[req.user.role] || 0;
    const requiredLevel = roleHierarchy[minRole] || 0;

    if (userLevel < requiredLevel) {
      return res.status(403).json({
        success: false,
        message: 'Insufficient permissions'
      });
    }

    next();
  };
};

// Optional authentication - doesn't fail if no token
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      return next();
    }

    const token = jwtUtils.extractTokenFromHeader(authHeader);
    const decoded = jwtUtils.verifyAccessToken(token);

    const user = await User.findById(decoded.id);
    if (user && user.is_active) {
      req.user = user;
    }

    next();
  } catch (error) {
    // Continue without authentication
    next();
  }
};

// Check if user owns resource or has admin/hr privileges
const authorizeOwnerOrAdmin = (getResourceOwnerId) => {
  return async (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    // Admin and HR can access any resource
    if (['admin', 'hr'].includes(req.user.role)) {
      return next();
    }

    try {
      const resourceOwnerId = await getResourceOwnerId(req);
      
      // Check if user owns the resource
      if (req.user.id === resourceOwnerId) {
        return next();
      }

      // For managers, check if they manage the employee
      if (req.user.role === 'manager') {
        const Employee = require('../models/Employee');
        const userEmployee = await Employee.findByUserId(req.user.id);
        const resourceEmployee = await Employee.findByUserId(resourceOwnerId);
        
        if (userEmployee && resourceEmployee && resourceEmployee.manager_id === userEmployee.id) {
          return next();
        }
      }

      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });

    } catch (error) {
      return res.status(500).json({
        success: false,
        message: 'Authorization check failed',
        error: error.message
      });
    }
  };
};

// Rate limiting middleware
const rateLimit = (windowMs = 15 * 60 * 1000, maxRequests = 100) => {
  const requests = new Map();

  return (req, res, next) => {
    const clientId = req.ip || req.connection.remoteAddress;
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean old entries
    for (const [id, timestamps] of requests.entries()) {
      const validTimestamps = timestamps.filter(time => time > windowStart);
      if (validTimestamps.length === 0) {
        requests.delete(id);
      } else {
        requests.set(id, validTimestamps);
      }
    }

    // Check current client
    const clientRequests = requests.get(clientId) || [];
    const validRequests = clientRequests.filter(time => time > windowStart);

    if (validRequests.length >= maxRequests) {
      return res.status(429).json({
        success: false,
        message: 'Too many requests, please try again later'
      });
    }

    // Add current request
    validRequests.push(now);
    requests.set(clientId, validRequests);

    next();
  };
};

// Audit logging middleware
const auditLog = (action) => {
  return async (req, res, next) => {
    const originalSend = res.send;
    
    res.send = function(data) {
      // Log the action after response
      setImmediate(async () => {
        try {
          await database.run(`
            INSERT INTO audit_logs (user_id, action, table_name, record_id, new_values, ip_address, user_agent)
            VALUES (?, ?, ?, ?, ?, ?, ?)
          `, [
            req.user?.id || null,
            action,
            req.params.table || null,
            req.params.id || null,
            JSON.stringify(req.body),
            req.ip,
            req.get('User-Agent')
          ]);
        } catch (error) {
          console.error('Audit log failed:', error);
        }
      });

      originalSend.call(this, data);
    };

    next();
  };
};

module.exports = {
  authenticate,
  authorize,
  authorizeMinRole,
  optionalAuth,
  authorizeOwnerOrAdmin,
  rateLimit,
  auditLog
};
