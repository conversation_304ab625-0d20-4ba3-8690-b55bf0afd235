# HRM Management System

A comprehensive Human Resource Management System with attendance tracking, employee management, and role-based access control.

## Features

- **User Authentication**: Secure signup/login with username & password
- **Role-Based Access**: Admin, HR Manager, Manager, and Employee roles
- **Attendance Management**: Clock in/out, attendance tracking, and reports
- **Employee Management**: Complete employee lifecycle management
- **Leave Management**: Leave requests, approvals, and balance tracking
- **Dashboard**: Role-specific dashboards with real-time data
- **Reports & Analytics**: Comprehensive reporting and data visualization

## Technology Stack

### Backend
- Node.js with Express.js
- SQLite database (development) / PostgreSQL (production)
- JWT authentication
- bcrypt password hashing

### Frontend
- HTML5/CSS3/JavaScript
- Bootstrap 5 for responsive design
- Chart.js for data visualization
- Font Awesome icons

## Quick Start

### Prerequisites
- Node.js (v16 or higher)
- npm (v8 or higher)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd attendance-system
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Initialize database:
```bash
npm run migrate
npm run seed
```

5. Start development server:
```bash
npm run dev
```

6. Open your browser and navigate to `http://localhost:3000`

## Default Login Credentials

### Admin User
- Username: `admin`
- Password: `admin123`

### Test Employee
- Username: `employee1`
- Password: `password123`

## Project Structure

```
attendance-system/
├── docs/                   # Documentation
├── src/
│   ├── frontend/          # Frontend assets
│   │   ├── css/          # Stylesheets
│   │   ├── js/           # JavaScript files
│   │   ├── pages/        # HTML pages
│   │   └── assets/       # Images, fonts, etc.
│   └── backend/          # Backend API
│       ├── controllers/  # Route controllers
│       ├── models/       # Database models
│       ├── routes/       # API routes
│       ├── middleware/   # Custom middleware
│       ├── config/       # Configuration files
│       └── utils/        # Utility functions
├── database/
│   ├── migrations/       # Database migrations
│   └── seeds/           # Seed data
├── tests/               # Test files
└── package.json
```

## API Documentation

### Authentication Endpoints
- `POST /api/auth/signup` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/profile` - Get user profile

### Employee Management
- `GET /api/employees` - Get all employees
- `POST /api/employees` - Create employee
- `GET /api/employees/:id` - Get employee details
- `PUT /api/employees/:id` - Update employee
- `DELETE /api/employees/:id` - Delete employee

### Attendance Management
- `POST /api/attendance/clock-in` - Clock in
- `POST /api/attendance/clock-out` - Clock out
- `GET /api/attendance/:employeeId` - Get attendance records
- `GET /api/attendance/reports` - Generate reports

## User Roles

### Admin
- Full system access
- User management
- System configuration
- All reports and analytics

### HR Manager
- Employee management
- Attendance oversight
- Leave approval
- HR reports

### Manager
- Team attendance monitoring
- Leave approval for team members
- Team performance reports

### Employee
- Personal attendance tracking
- Clock in/out
- Leave requests
- Personal reports

## Development

### Running Tests
```bash
npm test
npm run test:watch
```

### Code Linting
```bash
npm run lint
npm run lint:fix
```

### Database Operations
```bash
npm run migrate    # Run migrations
npm run seed      # Seed database
npm run build     # Run migrations and seed
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run tests and linting
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please contact [<EMAIL>]

## Changelog

### Version 1.0.0
- Initial release
- Basic authentication system
- Employee management
- Attendance tracking
- Role-based dashboards
- Leave management system
