const database = require('../config/database');

class Department {
  constructor(data = {}) {
    this.id = data.id;
    this.name = data.name;
    this.description = data.description;
    this.manager_id = data.manager_id;
    this.budget = data.budget;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  // Create new department
  static async create(departmentData) {
    const { name, description, manager_id, budget } = departmentData;

    const sql = `
      INSERT INTO departments (name, description, manager_id, budget)
      VALUES (?, ?, ?, ?)
    `;

    const result = await database.run(sql, [name, description, manager_id, budget]);
    return await Department.findById(result.id);
  }

  // Find department by ID
  static async findById(id) {
    const sql = 'SELECT * FROM departments WHERE id = ?';
    const row = await database.get(sql, [id]);
    return row ? new Department(row) : null;
  }

  // Find department by name
  static async findByName(name) {
    const sql = 'SELECT * FROM departments WHERE name = ?';
    const row = await database.get(sql, [name]);
    return row ? new Department(row) : null;
  }

  // Get all departments
  static async findAll() {
    const sql = 'SELECT * FROM departments ORDER BY name';
    const rows = await database.all(sql);
    return rows.map(row => new Department(row));
  }

  // Get department with manager details
  async getWithManager() {
    const sql = `
      SELECT d.*, 
             e.first_name as manager_first_name, 
             e.last_name as manager_last_name,
             e.employee_id as manager_employee_id
      FROM departments d
      LEFT JOIN employees e ON d.manager_id = e.id
      WHERE d.id = ?
    `;
    const row = await database.get(sql, [this.id]);
    return row;
  }

  // Get department employees
  async getEmployees() {
    const sql = 'SELECT * FROM employees WHERE department = ? AND status = "active"';
    const rows = await database.all(sql, [this.name]);
    return rows;
  }

  // Get department employee count
  async getEmployeeCount() {
    const sql = 'SELECT COUNT(*) as count FROM employees WHERE department = ? AND status = "active"';
    const result = await database.get(sql, [this.name]);
    return result.count || 0;
  }

  // Update department
  async update(updateData) {
    const allowedFields = ['name', 'description', 'manager_id', 'budget'];
    const updates = [];
    const params = [];

    for (const [key, value] of Object.entries(updateData)) {
      if (allowedFields.includes(key)) {
        updates.push(`${key} = ?`);
        params.push(value);
      }
    }

    if (updates.length === 0) {
      throw new Error('No valid fields to update');
    }

    updates.push('updated_at = CURRENT_TIMESTAMP');
    params.push(this.id);

    const sql = `UPDATE departments SET ${updates.join(', ')} WHERE id = ?`;
    await database.run(sql, params);

    // Refresh the instance
    const updated = await Department.findById(this.id);
    Object.assign(this, updated);
    return this;
  }

  // Delete department
  async delete() {
    // Check if department has employees
    const employeeCount = await this.getEmployeeCount();
    if (employeeCount > 0) {
      throw new Error('Cannot delete department with active employees');
    }

    const sql = 'DELETE FROM departments WHERE id = ?';
    await database.run(sql, [this.id]);
    return true;
  }

  // Validate department data
  static validate(departmentData) {
    const errors = [];

    if (!departmentData.name || departmentData.name.length < 2) {
      errors.push('Department name must be at least 2 characters long');
    }

    if (departmentData.budget && (isNaN(departmentData.budget) || departmentData.budget < 0)) {
      errors.push('Budget must be a positive number');
    }

    return errors;
  }

  // Convert to JSON
  toJSON() {
    return { ...this };
  }
}

module.exports = Department;
