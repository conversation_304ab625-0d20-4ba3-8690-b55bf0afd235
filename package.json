{"name": "hrm-attendance-system", "version": "1.0.0", "description": "Complete HRM Management System with attendance tracking, employee management, and role-based access control", "main": "src/backend/server.js", "scripts": {"start": "node src/backend/server.js", "dev": "nodemon src/backend/server.js", "test": "jest", "test:watch": "jest --watch", "migrate": "node database/migrations/migrate.js", "seed": "node database/seeds/seed.js", "build": "npm run migrate && npm run seed", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["hrm", "attendance", "employee-management", "nodejs", "express", "sqlite"], "author": "Your Company", "license": "MIT", "dependencies": {"express": "^4.18.2", "sqlite3": "^5.1.6", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "moment": "^2.29.4", "uuid": "^9.0.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3", "eslint": "^8.46.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.28.0", "eslint-plugin-n": "^16.0.1", "eslint-plugin-promise": "^6.1.1"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/yourcompany/hrm-attendance-system.git"}, "bugs": {"url": "https://github.com/yourcompany/hrm-attendance-system/issues"}, "homepage": "https://github.com/yourcompany/hrm-attendance-system#readme"}