const express = require('express');
const router = express.Router();
const AuthController = require('../controllers/authController');
const { authenticate, authorize, rateLimit, auditLog } = require('../middleware/auth');
const {
  validateSignup,
  validateLogin,
  validateProfileUpdate,
  validatePasswordChange,
  validateId
} = require('../middleware/validation');

// Rate limiting for auth routes
const authRateLimit = rateLimit(15 * 60 * 1000, 5); // 5 requests per 15 minutes
const loginRateLimit = rateLimit(15 * 60 * 1000, 3); // 3 login attempts per 15 minutes

/**
 * @route   POST /api/auth/signup
 * @desc    Register a new user
 * @access  Public
 */
router.post('/signup', 
  authRateLimit,
  validateSignup,
  auditLog('USER_SIGNUP'),
  AuthController.signup
);

/**
 * @route   POST /api/auth/login
 * @desc    Login user
 * @access  Public
 */
router.post('/login',
  loginRateLimit,
  validateLogin,
  auditLog('USER_LOGIN'),
  AuthController.login
);

/**
 * @route   POST /api/auth/logout
 * @desc    Logout user
 * @access  Private
 */
router.post('/logout',
  auditLog('USER_LOGOUT'),
  AuthController.logout
);

/**
 * @route   POST /api/auth/refresh
 * @desc    Refresh access token
 * @access  Public
 */
router.post('/refresh',
  authRateLimit,
  AuthController.refresh
);

/**
 * @route   GET /api/auth/profile
 * @desc    Get user profile
 * @access  Private
 */
router.get('/profile',
  authenticate,
  AuthController.getProfile
);

/**
 * @route   PUT /api/auth/profile
 * @desc    Update user profile
 * @access  Private
 */
router.put('/profile',
  authenticate,
  validateProfileUpdate,
  auditLog('PROFILE_UPDATE'),
  AuthController.updateProfile
);

/**
 * @route   PUT /api/auth/change-password
 * @desc    Change user password
 * @access  Private
 */
router.put('/change-password',
  authenticate,
  validatePasswordChange,
  auditLog('PASSWORD_CHANGE'),
  AuthController.changePassword
);

/**
 * @route   GET /api/auth/sessions
 * @desc    Get user sessions
 * @access  Private
 */
router.get('/sessions',
  authenticate,
  AuthController.getSessions
);

/**
 * @route   DELETE /api/auth/sessions/:id
 * @desc    Revoke a session
 * @access  Private
 */
router.delete('/sessions/:id',
  authenticate,
  validateId,
  auditLog('SESSION_REVOKE'),
  AuthController.revokeSession
);

/**
 * @route   GET /api/auth/verify
 * @desc    Verify token validity
 * @access  Private
 */
router.get('/verify',
  authenticate,
  (req, res) => {
    res.json({
      success: true,
      message: 'Token is valid',
      data: {
        user: req.user.toJSON(),
        session: {
          id: req.session.id,
          created_at: req.session.created_at,
          last_used: req.session.last_used,
          expires_at: req.session.expires_at
        }
      }
    });
  }
);

/**
 * @route   GET /api/auth/me
 * @desc    Get current user info (alias for profile)
 * @access  Private
 */
router.get('/me',
  authenticate,
  AuthController.getProfile
);

// Error handling middleware for auth routes
router.use((error, req, res, next) => {
  console.error('Auth route error:', error);
  
  if (error.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors: error.errors
    });
  }
  
  if (error.name === 'JsonWebTokenError') {
    return res.status(401).json({
      success: false,
      message: 'Invalid token'
    });
  }
  
  if (error.name === 'TokenExpiredError') {
    return res.status(401).json({
      success: false,
      message: 'Token expired'
    });
  }
  
  res.status(500).json({
    success: false,
    message: 'Internal server error',
    error: process.env.NODE_ENV === 'development' ? error.message : undefined
  });
});

module.exports = router;
