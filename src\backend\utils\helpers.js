const database = require('../config/database');

class Helpers {
  // Generate unique employee ID
  static async generateEmployeeId(prefix = 'EMP') {
    try {
      // Get the highest existing employee ID
      const result = await database.get(`
        SELECT employee_id 
        FROM employees 
        WHERE employee_id LIKE '${prefix}%' 
        ORDER BY CAST(SUBSTR(employee_id, ${prefix.length + 1}) AS INTEGER) DESC 
        LIMIT 1
      `);

      let nextNumber = 1;
      if (result && result.employee_id) {
        const currentNumber = parseInt(result.employee_id.substring(prefix.length));
        nextNumber = currentNumber + 1;
      }

      // Format with leading zeros (e.g., EMP001, EMP002, etc.)
      const paddedNumber = nextNumber.toString().padStart(3, '0');
      return `${prefix}${paddedNumber}`;

    } catch (error) {
      console.error('Error generating employee ID:', error);
      // Fallback to timestamp-based ID
      return `${prefix}${Date.now().toString().slice(-6)}`;
    }
  }

  // Generate username from employee data
  static generateUsername(firstName, lastName, employeeId) {
    // Try different combinations
    const combinations = [
      `${firstName.toLowerCase()}.${lastName.toLowerCase()}`,
      `${firstName.toLowerCase()}${lastName.toLowerCase()}`,
      `${firstName.toLowerCase()}.${lastName.toLowerCase().charAt(0)}`,
      `${firstName.toLowerCase().charAt(0)}.${lastName.toLowerCase()}`,
      employeeId.toLowerCase()
    ];

    return combinations[0]; // Return the first combination, validation will handle uniqueness
  }

  // Calculate working days between two dates (excluding weekends)
  static calculateWorkingDays(startDate, endDate) {
    const start = new Date(startDate);
    const end = new Date(endDate);
    let workingDays = 0;

    const currentDate = new Date(start);
    while (currentDate <= end) {
      const dayOfWeek = currentDate.getDay();
      // 0 = Sunday, 6 = Saturday
      if (dayOfWeek !== 0 && dayOfWeek !== 6) {
        workingDays++;
      }
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return workingDays;
  }

  // Calculate hours between two times
  static calculateHours(startTime, endTime) {
    if (!startTime || !endTime) return 0;

    const start = new Date(`1970-01-01T${startTime}`);
    const end = new Date(`1970-01-01T${endTime}`);
    
    if (end < start) {
      // Handle overnight shifts
      end.setDate(end.getDate() + 1);
    }

    const diffMs = end - start;
    return Math.round((diffMs / (1000 * 60 * 60)) * 100) / 100; // Round to 2 decimal places
  }

  // Format currency
  static formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  }

  // Format date
  static formatDate(date, format = 'YYYY-MM-DD') {
    const d = new Date(date);
    
    if (format === 'YYYY-MM-DD') {
      return d.toISOString().split('T')[0];
    }
    
    if (format === 'DD/MM/YYYY') {
      return `${d.getDate().toString().padStart(2, '0')}/${(d.getMonth() + 1).toString().padStart(2, '0')}/${d.getFullYear()}`;
    }
    
    if (format === 'MM/DD/YYYY') {
      return `${(d.getMonth() + 1).toString().padStart(2, '0')}/${d.getDate().toString().padStart(2, '0')}/${d.getFullYear()}`;
    }

    return d.toLocaleDateString();
  }

  // Validate email format
  static isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Validate phone number
  static isValidPhone(phone) {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone);
  }

  // Generate random password
  static generateRandomPassword(length = 8) {
    const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';
    
    // Ensure at least one of each required character type
    password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)]; // Uppercase
    password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)]; // Lowercase
    password += '0123456789'[Math.floor(Math.random() * 10)]; // Number
    password += '!@#$%^&*'[Math.floor(Math.random() * 8)]; // Special character
    
    // Fill the rest randomly
    for (let i = password.length; i < length; i++) {
      password += charset[Math.floor(Math.random() * charset.length)];
    }
    
    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('');
  }

  // Sanitize string for database
  static sanitizeString(str) {
    if (typeof str !== 'string') return str;
    return str.trim().replace(/[<>]/g, '');
  }

  // Get age from date of birth
  static calculateAge(dateOfBirth) {
    if (!dateOfBirth) return null;
    
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  }

  // Get years of service
  static calculateYearsOfService(hireDate) {
    if (!hireDate) return 0;
    
    const today = new Date();
    const hire = new Date(hireDate);
    const diffTime = Math.abs(today - hire);
    const diffYears = diffTime / (1000 * 60 * 60 * 24 * 365.25);
    
    return Math.floor(diffYears * 10) / 10; // Round to 1 decimal place
  }

  // Check if date is weekend
  static isWeekend(date) {
    const day = new Date(date).getDay();
    return day === 0 || day === 6; // Sunday or Saturday
  }

  // Get next working day
  static getNextWorkingDay(date) {
    const nextDay = new Date(date);
    nextDay.setDate(nextDay.getDate() + 1);
    
    while (this.isWeekend(nextDay)) {
      nextDay.setDate(nextDay.getDate() + 1);
    }
    
    return nextDay;
  }

  // Paginate array
  static paginate(array, page = 1, limit = 10) {
    const offset = (page - 1) * limit;
    const paginatedItems = array.slice(offset, offset + limit);
    
    return {
      data: paginatedItems,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: array.length,
        pages: Math.ceil(array.length / limit)
      }
    };
  }

  // Deep clone object
  static deepClone(obj) {
    return JSON.parse(JSON.stringify(obj));
  }

  // Remove sensitive data from object
  static removeSensitiveData(obj, sensitiveFields = ['password', 'password_hash', 'token']) {
    const cleaned = this.deepClone(obj);
    
    sensitiveFields.forEach(field => {
      if (cleaned.hasOwnProperty(field)) {
        delete cleaned[field];
      }
    });
    
    return cleaned;
  }

  // Generate slug from string
  static generateSlug(str) {
    return str
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, '')
      .replace(/[\s_-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }

  // Check if object is empty
  static isEmpty(obj) {
    return Object.keys(obj).length === 0;
  }

  // Get current timestamp
  static getCurrentTimestamp() {
    return new Date().toISOString();
  }

  // Convert time to minutes
  static timeToMinutes(time) {
    if (!time) return 0;
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }

  // Convert minutes to time
  static minutesToTime(minutes) {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:00`;
  }
}

module.exports = Helpers;
